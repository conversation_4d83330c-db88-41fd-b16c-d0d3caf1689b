--- /dev/null
+++ b/src/lib/extract/extractor_factory.cpp
@@ -0,0 +1,168 @@
+/**
+ * @file extractor_factory.cpp
+ * @brief Implementation of extractor factory and registry
+ * <AUTHOR> Cancer Data Engineering
+ * @date 2025
+ */
+
+#include "extract/extractor_factory.h"
+#include "extract/csv_extractor.h"
+#include "extract/json_extractor.h"
+#include "extract/database_connector.h"
+#include "extract/postgresql_connector.h"
+#ifdef OMOP_HAS_MYSQL
+#include "extract/mysql_connector.h"
+#endif
+#ifdef OMOP_HAS_ODBC
+#include "extract/odbc_connector.h"
+#endif
+#include <algorithm>
+#include <sstream>
+
+namespace omop::extract {
+
+// Static member definitions
+std::unordered_map<std::string, std::function<std::unique_ptr<core::IExtractor>()>> 
+    ExtractorFactoryRegistry::creators_;
+std::mutex ExtractorFactoryRegistry::mutex_;
+
+void ExtractorFactoryRegistry::register_type(const std::string& type,
+                                           std::function<std::unique_ptr<core::IExtractor>()> creator) {
+    std::lock_guard<std::mutex> lock(mutex_);
+    creators_[type] = std::move(creator);
+}
+
+std::unique_ptr<core::IExtractor> ExtractorFactoryRegistry::create(const std::string& type) {
+    std::lock_guard<std::mutex> lock(mutex_);
+    
+    auto it = creators_.find(type);
+    if (it != creators_.end()) {
+        return it->second();
+    }
+    
+    throw common::ConfigurationException("Unknown extractor type: '" + type + "'", "factory");
+}
+
+std::vector<std::string> ExtractorFactoryRegistry::get_registered_types() {
+    std::lock_guard<std::mutex> lock(mutex_);
+    
+    std::vector<std::string> types;
+    types.reserve(creators_.size());
+    
+    for (const auto& [type, creator] : creators_) {
+        types.push_back(type);
+    }
+    
+    std::sort(types.begin(), types.end());
+    return types;
+}
+
+bool ExtractorFactoryRegistry::is_type_registered(const std::string& type) {
+    std::lock_guard<std::mutex> lock(mutex_);
+    return creators_.find(type) != creators_.end();
+}
+
+void ExtractorFactoryRegistry::clear() {
+    std::lock_guard<std::mutex> lock(mutex_);
+    creators_.clear();
+}
+
+// Global initialization flag
+static std::once_flag g_extractors_initialized;
+
+void initialize_extractors() {
+    std::call_once(g_extractors_initialized, []() {
+        // Register CSV extractors
+        CsvExtractorFactory::register_extractors();
+        
+        // Register JSON extractors
+        JsonExtractorFactory::register_extractors();
+        
+        // Register database extractors
+        ExtractorFactoryRegistry::register_type("database", []() {
+            throw common::ConfigurationException(
+                "Database extractor requires a connection object", "database");
+        });
+        
+        // Register PostgreSQL
+        PostgreSQLRegistrar::register_components();
+        ExtractorFactoryRegistry::register_type("postgresql", []() {
+            auto conn = DatabaseConnectionFactory::instance().create("postgresql", {});
+            return std::make_unique<PostgreSQLExtractor>(std::move(conn));
+        });
+        
+        ExtractorFactoryRegistry::register_type("postgres", []() {
+            auto conn = DatabaseConnectionFactory::instance().create("postgres", {});
+            return std::make_unique<PostgreSQLExtractor>(std::move(conn), "postgres");
+        });
+        
+#ifdef OMOP_HAS_MYSQL
+        // Register MySQL
+        MySQLRegistrar::register_components();
+        ExtractorFactoryRegistry::register_type("mysql", []() {
+            auto conn = DatabaseConnectionFactory::instance().create("mysql", {});
+            return std::make_unique<MySQLExtractor>(std::move(conn));
+        });
+#endif
+        
+#ifdef OMOP_HAS_ODBC
+        // Register ODBC
+        ExtractorFactoryRegistry::register_type("odbc", []() {
+            auto conn = DatabaseConnectionFactory::instance().create("odbc", {});
+            return std::make_unique<OdbcExtractor>(std::move(conn));
+        });
+#endif
+        
+        // Register SQLite (stub for now)
+        ExtractorFactoryRegistry::register_type("sqlite", []() {
+            throw common::ConfigurationException("SQLite support not implemented", "sqlite");
+        });
+        
+        // Register Oracle (stub for now)
+        ExtractorFactoryRegistry::register_type("oracle", []() {
+            throw common::ConfigurationException("Oracle support not implemented", "oracle");
+        });
+        
+        // Register SQL Server (stub for now)
+        ExtractorFactoryRegistry::register_type("sqlserver", []() {
+            throw common::ConfigurationException("SQL Server support not implemented", "sqlserver");
+        });
+    });
+}
+
+void reset_initialize_extractors_flag() {
+    // Reset the initialization flag (for testing purposes)
+    std::call_once(g_extractors_initialized, []() {});
+    g_extractors_initialized = std::once_flag{};
+}
+
+std::unique_ptr<core::IExtractor> create_extractor(
+    const std::string& type,
+    const std::unordered_map<std::string, std::any>& config) {
+    
+    // Ensure extractors are initialized
+    initialize_extractors();
+    
+    auto extractor = ExtractorFactoryRegistry::create(type);
+    
+    if (extractor && !config.empty()) {
+        core::ProcessingContext context;
+        extractor->initialize(config, context);
+    }
+    
+    return extractor;
+}
+
+std::vector<ExtractorTypeInfo> get_extractor_info() {
+    initialize_extractors();
+    
+    std::vector<ExtractorTypeInfo> info_list;
+    
+    // Define extractor information
+    info_list.push_back({
+        "csv",
+        "Extract data from CSV files with configurable delimiters and options",
+        {"filepath"},
+        {"delimiter", "has_header", "encoding", "skip_lines", "max_lines"},
+        R"({"filepath": "data.csv", "delimiter": ",", "has_header": true})"
+    });
+    
+    info_list.push_back({
+        "multi_csv",
+        "Extract data from multiple CSV files sequentially",
+        {"filepaths"},
+        {"skip_headers_after_first"},
+        R"({"filepaths": ["file1.csv", "file2.csv"], "skip_headers_after_first": true})"
+    });
+    
+    info_list.push_back({
+        "csv_directory",
+        "Extract data from all CSV files in a directory",
+        {"directory"},
+        {"file_pattern", "recursive"},
+        R"({"directory": "/data/csv", "file_pattern": ".*\\.csv$", "recursive": true})"
+    });
+    
+    info_list.push_back({
+        "compressed_csv",
+        "Extract data from compressed CSV files (gzip, zip, bzip2, xz)",
+        {"filepath"},
+        {"compression_format", "cleanup_temp_file"},
+        R"({"filepath": "data.csv.gz", "cleanup_temp_file": true})"
+    });
+    
+    info_list.push_back({
+        "json",
+        "Extract data from JSON files with nested object support",
+        {"filepath"},
+        {"root_path", "flatten_nested", "parse_dates"},
+        R"({"filepath": "data.json", "root_path": "data.records", "flatten_nested": true})"
+    });
+    
+    info_list.push_back({
+        "jsonl",
+        "Extract data from JSON Lines files (one JSON object per line)",
+        {"filepath"},
+        {"parse_dates", "continue_on_error"},
+        R"({"filepath": "data.jsonl", "parse_dates": true})"
+    });
+    
+    info_list.push_back({
+        "postgresql",
+        "Extract data from PostgreSQL databases",
+        {"host", "database"},
+        {"port", "username", "password", "table", "schema", "filter"},
+        R"({"host": "localhost", "port": 5432, "database": "omop", "table": "person"})"
+    });
+    
+#ifdef OMOP_HAS_MYSQL
+    info_list.push_back({
+        "mysql",
+        "Extract data from MySQL/MariaDB databases",
+        {"host", "database"},
+        {"port", "username", "password", "table", "filter"},
+        R"({"host": "localhost", "port": 3306, "database": "omop", "table": "person"})"
+    });
+#endif
+    
+#ifdef OMOP_HAS_ODBC
+    info_list.push_back({
+        "odbc",
+        "Extract data from databases via ODBC",
+        {"DSN"}, // or {"Driver", "Server", "Database"}
+        {"username", "password", "table", "filter"},
+        R"({"DSN": "MyDataSource", "username": "user", "password": "pass", "table": "person"})"
+    });
+#endif
+    
+    return info_list;
+}
+
+void print_extractor_info(std::ostream& stream) {
+    auto info_list = get_extractor_info();
+    
+    stream << "Available Extractor Types:\n";
+    stream << "========================\n\n";
+    
+    for (const auto& info : info_list) {
+        stream << "Type: " << info.type << "\n";
+        stream << "Description: " << info.description << "\n";
+        
+        stream << "Required parameters: ";
+        for (size_t i = 0; i < info.required_params.size(); ++i) {
+            if (i > 0) stream << ", ";
+            stream << info.required_params[i];
+        }
+        stream << "\n";
+        
+        stream << "Optional parameters: ";
+        for (size_t i = 0; i < info.optional_params.size(); ++i) {
+            if (i > 0) stream << ", ";
+            stream << info.optional_params[i];
+        }
+        stream << "\n";
+        
+        stream << "Example configuration:\n";
+        stream << info.example_config << "\n\n";
+    }
+}
+
+} // namespace omop::extract