--- a/tests/unit/extract/csv_extractor_test.cpp
+++ b/tests/unit/extract/csv_extractor_test.cpp
@@ -53,6 +53,7 @@ protected:
 // ============================================================================
 
 // Tests field type conversion functionality
+// Tests conversion of various field types including UK formats
 TEST_F(CsvFieldParserTest, ConvertFieldTypes) {
     // Test integer conversion
     auto int_value = parser_->convert_field("42", "integer");
@@ -177,6 +178,7 @@ TEST_F(CsvFieldParserTest, ParseUKLocalizedFormats) {
 }
 
 // Tests comprehensive field parsing edge cases including UK formats
+// Tests edge cases in CSV field parsing with special characters and UK formats
 TEST_F(CsvFieldParserTest, ComprehensiveFieldParsingEdgeCases) {
     CsvOptions options;
     options.delimiter = ',';
@@ -271,6 +273,7 @@ TEST_F(CsvFieldParserTest, ComprehensiveFieldParsingEdgeCases) {
 
 
 // Tests field parsing with escape characters
+// Tests parsing of fields containing escape characters
 TEST_F(CsvFieldParserTest, ParseEscapedField) {
     std::string input = "field\\,with\\,escapes,normal field";
     size_t pos = 0;
@@ -280,6 +283,7 @@ TEST_F(CsvFieldParserTest, ParseEscapedField) {
 }
 
 // Tests parsing complete line into fields
+// Tests parsing a complete CSV line into individual fields
 TEST_F(CsvFieldParserTest, ParseCompleteLine) {
     std::string line = "John,Doe,30,\"New York, NY\"";
 
@@ -292,6 +296,7 @@ TEST_F(CsvFieldParserTest, ParseCompleteLine) {
 }
 
 // Tests field parsing with quoted fields
+// Tests parsing of quoted fields containing special characters
 TEST_F(CsvFieldParserTest, ParseQuotedField) {
     // Corrected input: both fields are quoted
     std::string input = "\"field with, comma\",\"field with \"\" quotes\"";
@@ -305,6 +310,7 @@ TEST_F(CsvFieldParserTest, ParseQuotedField) {
 }
 
 // Tests date parsing with UK formats (DD/MM/YYYY)
+// Tests parsing of dates in UK format (DD/MM/YYYY) and other formats
 TEST_F(CsvFieldParserTest, ParseDateField) {
     // Test UK date format DD/MM/YYYY
     options_.date_format = "%d/%m/%Y";
@@ -334,6 +340,7 @@ TEST_F(CsvFieldParserTest, ParseDateField) {
 }
 
 // Tests basic field parsing without quotes
+// Tests parsing of simple unquoted CSV fields
 TEST_F(CsvFieldParserTest, ParseSimpleField) {
     std::string input = "field1,field2,field3";
     size_t pos = 0;
@@ -351,6 +358,7 @@ TEST_F(CsvFieldParserTest, ParseSimpleField) {
 }
 
 // Tests type conversion error handling
+// Tests error handling when converting field types
 TEST_F(CsvFieldParserTest, TypeConversionErrorHandling) {
     CsvOptions options;
     options.delimiter = ',';