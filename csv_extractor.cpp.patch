--- /dev/null
+++ b/src/lib/extract/csv_extractor.cpp
@@ -0,0 +1,892 @@
+/**
+ * @file csv_extractor.cpp
+ * @brief Implementation of CSV extractor functionality
+ * <AUTHOR> Cancer Data Engineering
+ * @date 2025
+ */
+
+#include "extract/csv_extractor.h"
+#include "common/exceptions.h"
+#include "common/logging.h"
+#include <algorithm>
+#include <cctype>
+#include <ctime>
+#include <iomanip>
+#include <sstream>
+#include <regex>
+#include <zlib.h>
+#include <bzlib.h>
+#include <lzma.h>
+#include <zip.h>
+
+namespace omop::extract {
+
+// CsvFieldParser Implementation
+
+std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
+    std::vector<std::string> fields;
+    size_t pos = 0;
+    
+    while (pos < line.length()) {
+        fields.push_back(parse_field(line, pos));
+    }
+    
+    return fields;
+}
+
+std::any CsvFieldParser::convert_field(const std::string& field, 
+                                      const std::string& type_hint) {
+    // Handle null values
+    if (field.empty() || field == options_.null_string || 
+        field == "null" || field == "NULL") {
+        return std::any{};
+    }
+    
+    // If type hint is provided, use it
+    if (!type_hint.empty()) {
+        if (type_hint == "integer" || type_hint == "int" || type_hint == "long") {
+            try {
+                return static_cast<long long>(std::stoll(field));
+            } catch (...) {
+                return std::any{};
+            }
+        } else if (type_hint == "double" || type_hint == "float" || type_hint == "real") {
+            try {
+                return std::stod(field);
+            } catch (...) {
+                return std::any{};
+            }
+        } else if (type_hint == "boolean" || type_hint == "bool") {
+            std::string lower = field;
+            std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
+            
+            if (lower == "true" || lower == "t" || lower == "yes" || 
+                lower == "y" || lower == "on" || lower == "1") {
+                return true;
+            } else if (lower == "false" || lower == "f" || lower == "no" || 
+                      lower == "n" || lower == "off" || lower == "0") {
+                return false;
+            }
+            return std::any{};
+        } else if (type_hint == "date" || type_hint == "datetime" || type_hint == "timestamp") {
+            try {
+                std::string format = (type_hint == "date") ? options_.date_format : options_.datetime_format;
+                return parse_datetime(field, format);
+            } catch (...) {
+                return field; // Return as string if parsing fails
+            }
+        }
+        // Default to string
+        return field;
+    }
+    
+    // Auto-detect type
+    // Try boolean first (most restrictive)
+    std::string lower = field;
+    std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
+    if (lower == "true" || lower == "false") {
+        return lower == "true";
+    }
+    
+    // Try integer
+    try {
+        size_t idx;
+        long long val = std::stoll(field, &idx);
+        if (idx == field.length()) {
+            return val;
+        }
+    } catch (...) {}
+    
+    // Try double
+    try {
+        size_t idx;
+        double val = std::stod(field, &idx);
+        if (idx == field.length()) {
+            return val;
+        }
+    } catch (...) {}
+    
+    // Try date/time parsing with common formats
+    std::vector<std::string> date_formats = {
+        "%d/%m/%Y",           // UK date format
+        "%d-%m-%Y",           // UK date format alternative
+        "%d/%m/%Y %H:%M:%S", // UK datetime format
+        "%Y-%m-%d",           // ISO date
+        "%Y-%m-%d %H:%M:%S"  // ISO datetime
+    };
+    
+    for (const auto& format : date_formats) {
+        try {
+            auto result = parse_datetime(field, format);
+            return result;
+        } catch (...) {
+            // Continue trying other formats
+        }
+    }
+    
+    // Default to string
+    return field;
+}
+
+std::chrono::system_clock::time_point CsvFieldParser::parse_datetime(
+    const std::string& value, const std::string& format) const {
+    std::tm tm = {};
+    std::istringstream ss(value);
+    ss >> std::get_time(&tm, format.c_str());
+    
+    if (ss.fail()) {
+        throw std::runtime_error("Failed to parse datetime: " + value);
+    }
+    
+    auto time_t_val = std::mktime(&tm);
+    if (time_t_val == -1) {
+        throw std::runtime_error("Invalid datetime: " + value);
+    }
+    
+    return std::chrono::system_clock::from_time_t(time_t_val);
+}
+
+// CsvExtractor Implementation
+
+CsvExtractor::CsvExtractor() 
+    : parser_(options_) {
+}
+
+void CsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
+                             core::ProcessingContext& context) {
+    auto logger = common::Logger::get("omop-csv-extractor");
+    logger->info("Initializing CSV extractor");
+    
+    // Extract configuration
+    if (config.find("filepath") == config.end()) {
+        throw common::ExtractionException("CSV extractor requires 'filepath' parameter", "csv");
+    }
+    
+    filepath_ = std::any_cast<std::string>(config.at("filepath"));
+    
+    // Configure options
+    if (config.find("delimiter") != config.end()) {
+        std::string delim = std::any_cast<std::string>(config.at("delimiter"));
+        if (!delim.empty()) {
+            options_.delimiter = delim[0];
+        }
+    }
+    
+    if (config.find("quote_char") != config.end()) {
+        std::string quote = std::any_cast<std::string>(config.at("quote_char"));
+        if (!quote.empty()) {
+            options_.quote_char = quote[0];
+        }
+    }
+    
+    if (config.find("has_header") != config.end()) {
+        options_.has_header = std::any_cast<bool>(config.at("has_header"));
+    }
+    
+    if (config.find("encoding") != config.end()) {
+        options_.encoding = std::any_cast<std::string>(config.at("encoding"));
+    }
+    
+    if (config.find("skip_lines") != config.end()) {
+        options_.skip_lines = std::any_cast<size_t>(config.at("skip_lines"));
+    }
+    
+    if (config.find("max_lines") != config.end()) {
+        options_.max_lines = std::any_cast<size_t>(config.at("max_lines"));
+    }
+    
+    if (config.find("column_types") != config.end()) {
+        auto type_map = std::any_cast<std::unordered_map<std::string, std::string>>(config.at("column_types"));
+        for (const auto& [col, type] : type_map) {
+            auto it = std::find(column_names_.begin(), column_names_.end(), col);
+            if (it != column_names_.end()) {
+                size_t idx = std::distance(column_names_.begin(), it);
+                if (idx < column_types_.size()) {
+                    column_types_[idx] = type;
+                }
+            }
+        }
+    }
+    
+    if (config.find("infer_types") != config.end()) {
+        bool infer = std::any_cast<bool>(config.at("infer_types"));
+        if (infer) {
+            // Will be done after opening file
+        }
+    }
+    
+    // Create new parser with updated options
+    parser_ = CsvFieldParser(options_);
+    
+    // Open and prepare file
+    open_file(filepath_);
+    
+    // Read header if present
+    if (options_.has_header) {
+        read_header();
+    }
+    
+    // Type inference if requested
+    if (config.find("infer_types") != config.end() && 
+        std::any_cast<bool>(config.at("infer_types"))) {
+        infer_column_types();
+    }
+    
+    start_time_ = std::chrono::steady_clock::now();
+    stats_.start_time = std::chrono::system_clock::now();
+}
+
+core::RecordBatch CsvExtractor::extract_batch(size_t batch_size,
+                                             core::ProcessingContext& context) {
+    core::RecordBatch batch;
+    batch.reserve(batch_size);
+    
+    size_t extracted_in_batch = 0;
+    
+    while (extracted_in_batch < batch_size && has_more_data()) {
+        try {
+            std::string line;
+            if (!read_complete_record(line)) {
+                has_more_data_ = false;
+                break;
+            }
+            
+            // Skip empty lines if configured
+            if (line.empty() && options_.skip_empty_lines) {
+                continue;
+            }
+            
+            // Check max lines limit
+            if (options_.max_lines.has_value() && 
+                current_line_ > options_.skip_lines + options_.max_lines.value()) {
+                has_more_data_ = false;
+                break;
+            }
+            
+            auto fields = parser_.parse_line(line);
+            auto record = create_record(fields);
+            
+            batch.addRecord(std::move(record));
+            extracted_in_batch++;
+            stats_.successful_records++;
+            stats_.total_records++;
+            
+        } catch (const std::exception& e) {
+            stats_.failed_records++;
+            stats_.error_counts[e.what()]++;
+            
+            context.log("error", "Failed to extract CSV record at line " + 
+                       std::to_string(current_line_) + ": " + e.what());
+            context.increment_errors();
+            
+            if (!options_.skip_empty_lines) {
+                throw;
+            }
+        }
+    }
+    
+    return batch;
+}
+
+bool CsvExtractor::has_more_data() const {
+    return has_more_data_ && file_stream_.is_open() && !file_stream_.eof();
+}
+
+void CsvExtractor::finalize(core::ProcessingContext& context) {
+    file_stream_.close();
+    
+    auto end_time = std::chrono::steady_clock::now();
+    stats_.extraction_time_seconds = 
+        std::chrono::duration<double>(end_time - start_time_).count();
+    stats_.end_time = std::chrono::system_clock::now();
+    
+    stats_.calculate_rates();
+    
+    context.log("info", "CSV extraction completed. Extracted " + 
+               std::to_string(stats_.successful_records) + " records");
+}
+
+std::unordered_map<std::string, std::any> CsvExtractor::get_statistics() const {
+    std::unordered_map<std::string, std::any> stats;
+    
+    stats["total_records"] = stats_.total_records;
+    stats["successful_records"] = stats_.successful_records;
+    stats["failed_records"] = stats_.failed_records;
+    stats["extraction_time"] = stats_.extraction_time_seconds;
+    stats["records_per_second"] = stats_.records_per_second;
+    stats["current_line"] = current_line_;
+    stats["extracted_count"] = stats_.successful_records;
+    stats["error_count"] = stats_.failed_records;
+    
+    if (!stats_.error_counts.empty()) {
+        stats["error_types"] = stats_.error_counts;
+    }
+    
+    return stats;
+}
+
+bool CsvExtractor::is_compressed_file(const std::string& filepath) {
+    std::string lower_path = filepath;
+    std::transform(lower_path.begin(), lower_path.end(), lower_path.begin(), ::tolower);
+    
+    return lower_path.ends_with(".gz") || 
+           lower_path.ends_with(".zip") || 
+           lower_path.ends_with(".bz2") || 
+           lower_path.ends_with(".xz");
+}
+
+SourceSchema CsvExtractor::getSchema() const {
+    SourceSchema schema;
+    schema.source_name = filepath_;
+    schema.source_type = "csv";
+    
+    for (size_t i = 0; i < column_names_.size(); ++i) {
+        SourceSchema::Column col;
+        col.name = column_names_[i];
+        col.data_type = (i < column_types_.size()) ? column_types_[i] : "string";
+        col.nullable = true;
+        schema.columns.push_back(col);
+    }
+    
+    return schema;
+}
+
+omop::common::ValidationResult CsvExtractor::validateSource() {
+    omop::common::ValidationResult result;
+    
+    if (!std::filesystem::exists(filepath_)) {
+        result.add_error("file", "File does not exist: " + filepath_, "csv");
+    }
+    
+    if (!file_stream_.is_open()) {
+        result.add_error("file", "Cannot open file: " + filepath_, "csv");
+    }
+    
+    if (column_names_.empty() && options_.has_header) {
+        result.add_error("header", "No column headers found", "csv");
+    }
+    
+    return result;
+}
+
+bool CsvExtractor::connect() {
+    return file_stream_.is_open();
+}
+
+void CsvExtractor::disconnect() {
+    if (file_stream_.is_open()) {
+        file_stream_.close();
+    }
+}
+
+std::vector<core::Record> CsvExtractor::extractBatchImpl(size_t batch_size) {
+    std::vector<core::Record> records;
+    records.reserve(batch_size);
+    
+    for (size_t i = 0; i < batch_size && has_more_data(); ++i) {
+        std::string line;
+        if (read_complete_record(line)) {
+            if (!line.empty() || !options_.skip_empty_lines) {
+                auto fields = parser_.parse_line(line);
+                records.push_back(create_record(fields));
+            }
+        }
+    }
+    
+    return records;
+}
+
+core::Record CsvExtractor::convertToRecord(const std::any& source_data) {
+    if (source_data.type() == typeid(std::vector<std::string>)) {
+        const auto& fields = std::any_cast<std::vector<std::string>>(source_data);
+        return create_record(fields);
+    }
+    return core::Record();
+}
+
+void CsvExtractor::open_file(const std::string& filepath) {
+    auto logger = common::Logger::get("omop-csv-extractor");
+    
+    if (!std::filesystem::exists(filepath)) {
+        throw common::ExtractionException("File not found: " + filepath, "csv");
+    }
+    
+    file_stream_.open(filepath, std::ios::in | std::ios::binary);
+    if (!file_stream_.is_open()) {
+        throw common::ExtractionException("Failed to open file: " + filepath, "csv");
+    }
+    
+    // Skip initial lines if configured
+    for (size_t i = 0; i < options_.skip_lines; ++i) {
+        std::string dummy;
+        std::getline(file_stream_, dummy);
+        current_line_++;
+    }
+    
+    // Get file size for progress tracking
+    file_stream_.seekg(0, std::ios::end);
+    total_lines_ = 0; // Could implement line counting if needed
+    file_stream_.seekg(0, std::ios::beg);
+    
+    // Skip lines again after seeking
+    for (size_t i = 0; i < options_.skip_lines; ++i) {
+        std::string dummy;
+        std::getline(file_stream_, dummy);
+    }
+    
+    logger->info("Opened CSV file: {}", filepath);
+}
+
+void CsvExtractor::read_header() {
+    if (!file_stream_.is_open()) {
+        throw common::ExtractionException("File not open", "csv");
+    }
+    
+    std::string header_line;
+    if (!std::getline(file_stream_, header_line)) {
+        throw common::ExtractionException("Failed to read header", "csv");
+    }
+    
+    current_line_++;
+    
+    column_names_ = parser_.parse_line(header_line);
+    
+    // Initialize column types
+    column_types_.resize(column_names_.size(), "string");
+    
+    // Apply any column names from options
+    if (!options_.column_names.empty()) {
+        for (size_t i = 0; i < std::min(column_names_.size(), options_.column_names.size()); ++i) {
+            column_names_[i] = options_.column_names[i];
+        }
+    }
+}
+
+void CsvExtractor::infer_column_types(size_t sample_size) {
+    if (!file_stream_.is_open() || column_names_.empty()) {
+        return;
+    }
+    
+    // Save current position
+    auto pos = file_stream_.tellg();
+    
+    std::vector<std::unordered_map<std::string, int>> type_counts(column_names_.size());
+    size_t samples = 0;
+    
+    while (samples < sample_size && !file_stream_.eof()) {
+        std::string line;
+        if (std::getline(file_stream_, line)) {
+            if (line.empty() && options_.skip_empty_lines) {
+                continue;
+            }
+            
+            auto fields = parser_.parse_line(line);
+            for (size_t i = 0; i < std::min(fields.size(), column_names_.size()); ++i) {
+                auto value = parser_.convert_field(fields[i], "");
+                
+                if (!value.has_value()) {
+                    type_counts[i]["null"]++;
+                } else if (value.type() == typeid(bool)) {
+                    type_counts[i]["boolean"]++;
+                } else if (value.type() == typeid(long long)) {
+                    type_counts[i]["integer"]++;
+                } else if (value.type() == typeid(double)) {
+                    type_counts[i]["double"]++;
+                } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
+                    type_counts[i]["datetime"]++;
+                } else {
+                    type_counts[i]["string"]++;
+                }
+            }
+            samples++;
+        }
+    }
+    
+    // Determine column types based on majority
+    for (size_t i = 0; i < column_types_.size(); ++i) {
+        std::string best_type = "string";
+        int max_count = 0;
+        
+        for (const auto& [type, count] : type_counts[i]) {
+            if (type != "null" && count > max_count) {
+                max_count = count;
+                best_type = type;
+            }
+        }
+        
+        column_types_[i] = best_type;
+    }
+    
+    // Restore file position
+    file_stream_.clear();
+    file_stream_.seekg(pos);
+}
+
+core::Record CsvExtractor::create_record(const std::vector<std::string>& fields) {
+    core::Record record;
+    
+    for (size_t i = 0; i < std::min(fields.size(), column_names_.size()); ++i) {
+        std::string type_hint = (i < column_types_.size()) ? column_types_[i] : "";
+        auto value = parser_.convert_field(fields[i], type_hint);
+        
+        if (value.has_value()) {
+            record.setField(column_names_[i], value);
+        }
+    }
+    
+    return record;
+}
+
+bool CsvExtractor::read_complete_record(std::string& line) {
+    if (!file_stream_.is_open() || file_stream_.eof()) {
+        return false;
+    }
+    
+    line.clear();
+    std::string temp_line;
+    bool in_quotes = false;
+    int quote_count = 0;
+    
+    while (std::getline(file_stream_, temp_line)) {
+        current_line_++;
+        
+        // Count quotes to handle multi-line fields
+        for (char c : temp_line) {
+            if (c == options_.quote_char) {
+                quote_count++;
+            }
+        }
+        
+        in_quotes = (quote_count % 2 != 0);
+        
+        if (!line.empty()) {
+            line += '\n'; // Add newline for multi-line fields
+        }
+        line += temp_line;
+        
+        if (!in_quotes) {
+            return true;
+        }
+    }
+    
+    // If we're here, we hit EOF while in quotes
+    if (!line.empty()) {
+        // Return what we have, even if incomplete
+        return true;
+    }
+    
+    return false;
+}
+
+// MultiFileCsvExtractor Implementation
+
+void MultiFileCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
+                                      core::ProcessingContext& context) {
+    if (config.find("filepaths") == config.end()) {
+        throw common::ExtractionException("MultiFileCsvExtractor requires 'filepaths' parameter", "multi_csv");
+    }
+    
+    file_paths_ = std::any_cast<std::vector<std::string>>(config.at("filepaths"));
+    
+    if (file_paths_.empty()) {
+        throw common::ExtractionException("No files provided to MultiFileCsvExtractor", "multi_csv");
+    }
+    
+    if (config.find("skip_headers_after_first") != config.end()) {
+        skip_headers_after_first_ = std::any_cast<bool>(config.at("skip_headers_after_first"));
+    }
+    
+    // Initialize with first file
+    auto first_file_config = config;
+    first_file_config["filepath"] = file_paths_[0];
+    CsvExtractor::initialize(first_file_config, context);
+}
+
+core::RecordBatch MultiFileCsvExtractor::extract_batch(size_t batch_size,
+                                                       core::ProcessingContext& context) {
+    core::RecordBatch batch;
+    batch.reserve(batch_size);
+    
+    while (batch.size() < batch_size && (has_more_data() || current_file_index_ < file_paths_.size() - 1)) {
+        if (!has_more_data() && next_file()) {
+            // Continue with next file
+        }
+        
+        auto file_batch = CsvExtractor::extract_batch(batch_size - batch.size(), context);
+        
+        for (auto& record : file_batch.getRecords()) {
+            batch.addRecord(std::move(record));
+        }
+        
+        if (file_batch.empty() && !has_more_data()) {
+            // Current file exhausted, try next
+            if (!next_file()) {
+                break;
+            }
+        }
+    }
+    
+    return batch;
+}
+
+bool MultiFileCsvExtractor::has_more_data() const {
+    return CsvExtractor::has_more_data() || current_file_index_ < file_paths_.size() - 1;
+}
+
+bool MultiFileCsvExtractor::next_file() {
+    if (current_file_index_ >= file_paths_.size() - 1) {
+        return false;
+    }
+    
+    // Close current file
+    disconnect();
+    
+    current_file_index_++;
+    
+    // Open next file
+    open_file(file_paths_[current_file_index_]);
+    
+    // Skip header if configured
+    if (skip_headers_after_first_ && options_.has_header) {
+        std::string dummy;
+        read_complete_record(dummy);
+    }
+    
+    has_more_data_ = true;
+    return true;
+}
+
+// CsvDirectoryExtractor Implementation
+
+void CsvDirectoryExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
+                                      core::ProcessingContext& context) {
+    if (config.find("directory") == config.end()) {
+        throw common::ExtractionException("CsvDirectoryExtractor requires 'directory' parameter", "csv_directory");
+    }
+    
+    directory_path_ = std::any_cast<std::string>(config.at("directory"));
+    
+    if (!std::filesystem::exists(directory_path_) || !std::filesystem::is_directory(directory_path_)) {
+        throw common::ExtractionException("Invalid directory: " + directory_path_, "csv_directory");
+    }
+    
+    std::string pattern = ".*\\.csv$";
+    if (config.find("file_pattern") != config.end()) {
+        pattern = std::any_cast<std::string>(config.at("file_pattern"));
+    }
+    
+    if (config.find("recursive") != config.end()) {
+        recursive_search_ = std::any_cast<bool>(config.at("recursive"));
+    }
+    
+    file_paths_ = find_csv_files(directory_path_, pattern, recursive_search_);
+    
+    if (file_paths_.empty()) {
+        throw common::ExtractionException("No CSV files found in directory: " + directory_path_, "csv_directory");
+    }
+    
+    // Sort files for consistent ordering
+    std::sort(file_paths_.begin(), file_paths_.end());
+    
+    // Initialize with parent class
+    auto multi_config = config;
+    multi_config["filepaths"] = file_paths_;
+    MultiFileCsvExtractor::initialize(multi_config, context);
+}
+
+std::vector<std::string> CsvDirectoryExtractor::find_csv_files(const std::string& directory,
+                                                               const std::string& pattern,
+                                                               bool recursive) {
+    std::vector<std::string> csv_files;
+    std::regex file_regex(pattern, std::regex_constants::icase);
+    
+    if (recursive) {
+        for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
+            if (entry.is_regular_file() && 
+                std::regex_match(entry.path().filename().string(), file_regex)) {
+                csv_files.push_back(entry.path().string());
+            }
+        }
+    } else {
+        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
+            if (entry.is_regular_file() && 
+                std::regex_match(entry.path().filename().string(), file_regex)) {
+                csv_files.push_back(entry.path().string());
+            }
+        }
+    }
+    
+    return csv_files;
+}
+
+// CompressedCsvExtractor Implementation
+
+void CompressedCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
+                                       core::ProcessingContext& context) {
+    if (config.find("filepath") == config.end()) {
+        throw common::ExtractionException("CompressedCsvExtractor requires 'filepath' parameter", "compressed_csv");
+    }
+    
+    std::string compressed_path = std::any_cast<std::string>(config.at("filepath"));
+    
+    // Detect compression format
+    if (config.find("compression_format") != config.end()) {
+        compression_format_ = string_to_format(std::any_cast<std::string>(config.at("compression_format")));
+    } else {
+        compression_format_ = detect_compression(compressed_path);
+    }
+    
+    if (config.find("cleanup_temp_file") != config.end()) {
+        cleanup_temp_file_ = std::any_cast<bool>(config.at("cleanup_temp_file"));
+    }
+    
+    // Decompress file if needed
+    if (compression_format_ != CompressionFormat::None) {
+        temp_file_path_ = decompress_file(compressed_path, compression_format_);
+        
+        // Update config to use decompressed file
+        auto csv_config = config;
+        csv_config["filepath"] = temp_file_path_;
+        CsvExtractor::initialize(csv_config, context);
+    } else {
+        // Not compressed, use as regular CSV
+        CsvExtractor::initialize(config, context);
+    }
+}
+
+void CompressedCsvExtractor::finalize(core::ProcessingContext& context) {
+    CsvExtractor::finalize(context);
+    
+    // Clean up temporary file if configured
+    if (cleanup_temp_file_ && !temp_file_path_.empty() && 
+        std::filesystem::exists(temp_file_path_)) {
+        std::filesystem::remove(temp_file_path_);
+    }
+}
+
+std::unordered_map<std::string, std::any> CompressedCsvExtractor::get_statistics() const {
+    auto stats = CsvExtractor::get_statistics();
+    
+    stats["compression_format"] = format_to_string(compression_format_);
+    
+    if (!temp_file_path_.empty() && std::filesystem::exists(temp_file_path_)) {
+        stats["decompressed_size"] = std::filesystem::file_size(temp_file_path_);
+    }
+    
+    if (!filepath_.empty() && std::filesystem::exists(filepath_)) {
+        stats["compressed_size"] = std::filesystem::file_size(filepath_);
+        stats["original_size"] = stats["decompressed_size"];
+    }
+    
+    return stats;
+}
+
+CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::detect_compression(const std::string& filepath) {
+    std::string lower_path = filepath;
+    std::transform(lower_path.begin(), lower_path.end(), lower_path.begin(), ::tolower);
+    
+    if (lower_path.ends_with(".gz")) {
+        return CompressionFormat::Gzip;
+    } else if (lower_path.ends_with(".zip")) {
+        return CompressionFormat::Zip;
+    } else if (lower_path.ends_with(".bz2")) {
+        return CompressionFormat::Bzip2;
+    } else if (lower_path.ends_with(".xz")) {
+        return CompressionFormat::Xz;
+    }
+    
+    return CompressionFormat::None;
+}
+
+std::string CompressedCsvExtractor::decompress_file(const std::string& filepath,
+                                                   CompressionFormat format) {
+    // Create temporary file path
+    std::filesystem::path temp_path = std::filesystem::temp_directory_path();
+    temp_path /= "omop_csv_" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()) + ".csv";
+    
+    switch (format) {
+        case CompressionFormat::Gzip:
+            decompress_gzip(filepath, temp_path.string());
+            break;
+        case CompressionFormat::Zip:
+            decompress_zip(filepath, temp_path.string());
+            break;
+        case CompressionFormat::Bzip2:
+            decompress_bzip2(filepath, temp_path.string());
+            break;
+        case CompressionFormat::Xz:
+            decompress_xz(filepath, temp_path.string());
+            break;
+        default:
+            throw common::ExtractionException("Unsupported compression format", "compressed_csv");
+    }
+    
+    return temp_path.string();
+}
+
+void CompressedCsvExtractor::decompress_gzip(const std::string& src, const std::string& dst) {
+    gzFile gz = gzopen(src.c_str(), "rb");
+    if (!gz) {
+        throw common::ExtractionException("Failed to open gzip file: " + src, "compressed_csv");
+    }
+    
+    std::ofstream out(dst, std::ios::binary);
+    if (!out) {
+        gzclose(gz);
+        throw common::ExtractionException("Failed to create output file: " + dst, "compressed_csv");
+    }
+    
+    char buffer[8192];
+    int bytes_read;
+    
+    while ((bytes_read = gzread(gz, buffer, sizeof(buffer))) > 0) {
+        out.write(buffer, bytes_read);
+    }
+    
+    gzclose(gz);
+    out.close();
+    
+    if (bytes_read < 0) {
+        std::filesystem::remove(dst);
+        throw common::ExtractionException("Error decompressing gzip file", "compressed_csv");
+    }
+}
+
+void CompressedCsvExtractor::decompress_zip(const std::string& src, const std::string& dst) {
+    int err = 0;
+    zip_t* za = zip_open(src.c_str(), 0, &err);
+    if (!za) {
+        throw common::ExtractionException("Failed to open zip file: " + src, "compressed_csv");
+    }
+    
+    // Find first CSV file in archive
+    zip_int64_t num_entries = zip_get_num_entries(za, 0);
+    zip_int64_t csv_index = -1;
+    
+    for (zip_int64_t i = 0; i < num_entries; ++i) {
+        const char* name = zip_get_name(za, i, 0);
+        if (name && std::string(name).ends_with(".csv")) {
+            csv_index = i;
+            break;
+        }
+    }
+    
+    if (csv_index < 0) {
+        zip_close(za);
+        throw common::ExtractionException("No CSV file found in zip archive", "compressed_csv");
+    }
+    
+    zip_file_t* zf = zip_fopen_index(za, csv_index, 0);
+    if (!zf) {
+        zip_close(za);
+        throw common::ExtractionException("Failed to open CSV file in zip archive", "compressed_csv");
+    }
+    
+    std::ofstream out(dst, std::ios::binary);
+    char buffer[8192];
+    zip_int64_t bytes_read;
+    
+    while ((bytes_read = zip_fread(zf, buffer, sizeof(buffer))) > 0) {
+        out.write(buffer, bytes_read);
+    }
+    
+    zip_fclose(zf);
+    zip_close(za);
+    out.close();
+}
+
+void CompressedCsvExtractor::decompress_bzip2(const std::string& src, const std::string& dst) {
+    FILE* f = fopen(src.c_str(), "rb");
+    if (!f) {
+        throw common::ExtractionException("Failed to open bzip2 file: " + src, "compressed_csv");
+    }
+    
+    int bzerror;
+    BZFILE* b = BZ2_bzReadOpen(&bzerror, f, 0, 0, nullptr, 0);
+    if (bzerror != BZ_OK) {
+        fclose(f);
+        throw common::ExtractionException("Failed to initialize bzip2 decompression", "compressed_csv");
+    }
+    
+    std::ofstream out(dst, std::ios::binary);
+    char buffer[8192];
+    
+    while (bzerror == BZ_OK) {
+        int bytes_read = BZ2_bzRead(&bzerror, b, buffer, sizeof(buffer));
+        if (bytes_read > 0) {
+            out.write(buffer, bytes_read);
+        }
+    }
+    
+    BZ2_bzReadClose(&bzerror, b);
+    fclose(f);
+    out.close();
+    
+    if (bzerror != BZ_STREAM_END) {
+        std::filesystem::remove(dst);
+        throw common::ExtractionException("Error decompressing bzip2 file", "compressed_csv");
+    }
+}
+
+void CompressedCsvExtractor::decompress_xz(const std::string& src, const std::string& dst) {
+    // XZ decompression implementation would go here
+    // For now, throw not implemented
+    throw common::ExtractionException("XZ decompression not implemented", "compressed_csv");
+}
+
+std::string CompressedCsvExtractor::format_to_string(CompressionFormat format) const {
+    switch (format) {
+        case CompressionFormat::None: return "none";
+        case CompressionFormat::Gzip: return "gzip";
+        case CompressionFormat::Zip: return "zip";
+        case CompressionFormat::Bzip2: return "bzip2";
+        case CompressionFormat::Xz: return "xz";
+        default: return "unknown";
+    }
+}
+
+CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::string_to_format(const std::string& format_str) {
+    std::string lower = format_str;
+    std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
+    
+    if (lower == "gzip" || lower == "gz") return CompressionFormat::Gzip;
+    if (lower == "zip") return CompressionFormat::Zip;
+    if (lower == "bzip2" || lower == "bz2") return CompressionFormat::Bzip2;
+    if (lower == "xz") return CompressionFormat::Xz;
+    
+    return CompressionFormat::None;
+}
+
+// CsvExtractorFactory Implementation
+
+std::unique_ptr<core::IExtractor> CsvExtractorFactory::create(const std::string& type) {
+    if (type == "csv") {
+        return std::make_unique<CsvExtractor>();
+    } else if (type == "multi_csv") {
+        return std::make_unique<MultiFileCsvExtractor>();
+    } else if (type == "csv_directory") {
+        return std::make_unique<CsvDirectoryExtractor>();
+    } else if (type == "compressed_csv") {
+        return std::make_unique<CompressedCsvExtractor>();
+    }
+    
+    throw common::ConfigurationException("Unknown CSV extractor type: " + type, "csv");
+}
+
+void CsvExtractorFactory::register_extractors() {
+    ExtractorFactoryRegistry::register_type("csv", []() {
+        return std::make_unique<CsvExtractor>();
+    });
+    
+    ExtractorFactoryRegistry::register_type("multi_csv", []() {
+        return std::make_unique<MultiFileCsvExtractor>();
+    });
+    
+    ExtractorFactoryRegistry::register_type("csv_directory", []() {
+        return std::make_unique<CsvDirectoryExtractor>();
+    });
+    
+    ExtractorFactoryRegistry::register_type("compressed_csv", []() {
+        return std::make_unique<CompressedCsvExtractor>();
+    });
+}
+
+// Private helper methods for CompressedCsvExtractor (declarations were missing)
+void CompressedCsvExtractor::decompress_gzip(const std::string& src, const std::string& dst);
+void CompressedCsvExtractor::decompress_zip(const std::string& src, const std::string& dst);
+void CompressedCsvExtractor::decompress_bzip2(const std::string& src, const std::string& dst);
+void CompressedCsvExtractor::decompress_xz(const std::string& src, const std::string& dst);
+
+} // namespace omop::extract