--- /dev/null
+++ b/src/lib/extract/CMakeLists.txt
@@ -0,0 +1,74 @@
+# Extract library CMakeLists.txt
+
+# Source files
+set(EXTRACT_SOURCES
+    csv_extractor.cpp
+    json_extractor.cpp
+    extractor_base.cpp
+    extractor_factory.cpp
+    database_connector.cpp
+    postgresql_connector.cpp
+    extract_utils.cpp
+)
+
+# Platform-specific sources
+if(WIN32)
+    list(APPEND EXTRACT_SOURCES platform/windows_utils.cpp)
+else()
+    list(APPEND EXTRACT_SOURCES platform/unix_utils.cpp)
+endif()
+
+# Optional database connectors
+if(OMOP_HAS_MYSQL)
+    list(APPEND EXTRACT_SOURCES mysql_connector.cpp)
+endif()
+
+if(OMOP_HAS_ODBC)
+    list(APPEND EXTRACT_SOURCES odbc_connector.cpp)
+endif()
+
+# Create library
+add_library(omop_extract ${EXTRACT_SOURCES})
+
+# Include directories
+target_include_directories(omop_extract
+    PUBLIC
+        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
+        $<INSTALL_INTERFACE:include>
+)
+
+# Link dependencies
+target_link_libraries(omop_extract
+    PUBLIC
+        omop::core
+        omop::common
+        nlohmann_json::nlohmann_json
+        PostgreSQL::PostgreSQL
+        ZLIB::ZLIB
+    PRIVATE
+        Threads::Threads
+)
+
+# Optional dependencies
+if(OMOP_HAS_MYSQL)
+    target_link_libraries(omop_extract PUBLIC MySQL::MySQL)
+    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_MYSQL)
+endif()
+
+if(OMOP_HAS_ODBC)
+    target_link_libraries(omop_extract PUBLIC ODBC::ODBC)
+    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_ODBC)
+endif()
+
+# Platform-specific libraries
+if(UNIX AND NOT APPLE)
+    target_link_libraries(omop_extract PRIVATE rt)
+endif()
+
+# Set properties
+set_target_properties(omop_extract PROPERTIES
+    CXX_STANDARD 20
+    CXX_STANDARD_REQUIRED YES
+    CXX_EXTENSIONS NO
+)
+
+# Add alias
+add_library(omop::extract ALIAS omop_extract)