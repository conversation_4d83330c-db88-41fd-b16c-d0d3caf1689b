/**
 * @file omop_tables_test.cpp
 * @brief Unit tests for OMOP CDM table classes with UK healthcare localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"
#include <chrono>
#include <memory>
#include <locale>
#include <iomanip>
#include <sstream>
#include <thread>
#include <atomic>
#include <limits>

using namespace omop::cdm;
using namespace std::chrono;

namespace omop::cdm::test {

// Test helper class to access protected methods with UK healthcare context
class TestOmopTable : public OmopTable {
public:
    // Expose protected methods for testing
    using OmopTable::format_datetime_sql;
    using OmopTable::escape_string_sql;

    // Required virtual method implementations (minimal for testing)
    [[nodiscard]] std::string table_name() const override { return "uk_test_table"; }
    [[nodiscard]] std::string to_insert_sql(bool = true) const override { return ""; }
    [[nodiscard]] std::vector<std::string> field_names() const override { return {}; }
    [[nodiscard]] std::vector<std::any> field_values() const override { return {}; }
    void visit_fields(FieldVisitor&) const override {}
    [[nodiscard]] bool validate() const override { return true; }
    [[nodiscard]] std::vector<std::string> validation_errors() const override { return {}; }
};

// Test fixture for UK-localized tests
class UKLocaleTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_time_format_ = "%H:%M";
        
        // UK healthcare context dates
        uk_start_date_ = system_clock::now() - hours(72); // 3 days ago
        uk_end_date_ = system_clock::now() - hours(24);   // 1 day ago
        uk_current_time_ = system_clock::now();
    }

    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_time_format_;
    system_clock::time_point uk_start_date_;
    system_clock::time_point uk_end_date_;
    system_clock::time_point uk_current_time_;

    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }

    // Helper to format UK temperature
    std::string formatUKTemperature(double celsius) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << celsius << "°C";
        return oss.str();
    }
};

// Test bulk UK record creation performance for NHS systems
TEST_F(UKLocaleTest, BulkUKRecordCreationPerformsEfficientlyForNHSSystems) {
    auto start = std::chrono::high_resolution_clock::now();
    
    std::vector<std::unique_ptr<OmopTable>> uk_records;
    for (int i = 0; i < 1000; ++i) {
        auto person = std::make_unique<Person>();
        person->person_id = i;
        person->gender_concept_id = (i % 2 == 0) ? 8507 : 8532; // Male/Female UK concepts
        person->year_of_birth = 1950 + (i % 70);
        person->race_concept_id = 8552; // UK White ethnicity
        person->ethnicity_concept_id = 38003564; // Not Hispanic
        person->person_source_value = "NHS-" + std::to_string(********** + i); // UK NHS number format
        uk_records.push_back(std::move(person));
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    EXPECT_LT(duration.count(), 500); // Should create 1000 UK records in under 500ms (adjusted for debug/valgrind)
    EXPECT_EQ(uk_records.size(), 1000);
}

// Test condition occurrence with UK medical terminology
TEST_F(UKLocaleTest, ConditionOccurrenceHandlesUKMedicalTerminologyCorrectly) {
    auto condition = std::make_unique<ConditionOccurrence>();
    condition->condition_occurrence_id = 1001;
    condition->person_id = 123;
    condition->condition_concept_id = 201820; // SNOMED CT concept
    condition->condition_start_date = uk_start_date_;
    condition->condition_type_concept_id = 44818517; // UK EHR record
    condition->stop_reason = "Patient recovered";
    condition->condition_source_value = "M79.3"; // ICD-10 code used in UK
    condition->provider_id = 456; // UK GP practice
    
    EXPECT_TRUE(condition->validate());
    
    std::string sql = condition->to_insert_sql();
    EXPECT_TRUE(sql.find("'Patient recovered'") != std::string::npos);
    EXPECT_TRUE(sql.find("'M79.3'") != std::string::npos);
}

// Test condition occurrence validation errors include UK context
TEST_F(UKLocaleTest, ConditionOccurrenceValidationErrorsIncludeUKContext) {
    auto condition = std::make_unique<ConditionOccurrence>();
    condition->condition_occurrence_id = 0; // Invalid for UK NHS system
    auto errors = condition->validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("condition_occurrence_id") != std::string::npos; }));
}

// Test death table with UK mortality data
TEST_F(UKLocaleTest, DeathTableHandlesUKMortalityDataCorrectly) {
    auto death = std::make_unique<Death>();
    death->person_id = 123;
    death->death_date = uk_start_date_; // UK date format
    death->death_type_concept_id = 44818516; // UK death certificate
    death->cause_concept_id = 4306655; // UK cause of death
    death->cause_source_value = "Cardiac arrest";
    
    EXPECT_TRUE(death->validate());
    EXPECT_EQ(death->table_name(), "death");
    
    std::string sql = death->to_insert_sql();
    EXPECT_TRUE(sql.find("44818516") != std::string::npos);
    EXPECT_TRUE(sql.find("4306655") != std::string::npos);
    EXPECT_TRUE(sql.find("'Cardiac arrest'") != std::string::npos);
}

// Test death table validates UK future date restrictions
TEST_F(UKLocaleTest, DeathTableValidatesUKFutureDateRestrictions) {
    auto death = std::make_unique<Death>();
    death->person_id = 123;
    death->death_date = uk_current_time_ + hours(24); // Future date not allowed in UK
    EXPECT_FALSE(death->validate());
    auto errors = death->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("future") != std::string::npos; }));
}

// Test drug exposure with UK pharmaceutical data
TEST_F(UKLocaleTest, DrugExposureHandlesUKPharmaceuticalDataCorrectly) {
    auto drug = std::make_unique<DrugExposure>();
    drug->drug_exposure_id = 2001;
    drug->person_id = 123;
    drug->drug_concept_id = 1127078; // UK BNF drug concept
    drug->drug_exposure_start_date = uk_start_date_;
    drug->drug_exposure_end_date = uk_end_date_;
    drug->drug_type_concept_id = 38000177; // UK prescription
    drug->quantity = 30.0; // 30 tablets
    drug->days_supply = 30; // 30 days supply
    drug->sig = "Take 1 tablet by mouth once daily with food";
    drug->drug_source_value = "Lisinopril 10mg tablet";
    drug->lot_number = "LOT#123'456"; // UK pharmaceutical lot
    
    EXPECT_TRUE(drug->validate());
    EXPECT_EQ(drug->table_name(), "drug_exposure");
}

// Test drug exposure SQL escaping for UK pharmaceutical data
TEST_F(UKLocaleTest, DrugExposureSQLEscapingValidatesUKPharmaceuticalData) {
    auto drug = std::make_unique<DrugExposure>();
    drug->drug_exposure_id = 2001;
    drug->person_id = 123;
    drug->drug_concept_id = 1127078;
    drug->drug_exposure_start_date = uk_start_date_;
    drug->drug_exposure_end_date = uk_end_date_;
    drug->drug_type_concept_id = 38000177;
    drug->sig = "Take 1 tablet by mouth once daily with food";
    drug->drug_source_value = "Lisinopril 10mg tablet";
    drug->lot_number = "LOT#123'456"; // UK pharmaceutical lot with apostrophe
    
    std::string sql = drug->to_insert_sql(true);
    EXPECT_TRUE(sql.find("LOT#123''456") != std::string::npos); // Check quote escaping
}

// Test drug exposure validates UK pharmaceutical quantities
TEST_F(UKLocaleTest, DrugExposureValidatesUKPharmaceuticalQuantities) {
    auto drug = std::make_unique<DrugExposure>();
    drug->drug_exposure_id = 2001;
    drug->person_id = 123;
    drug->drug_concept_id = 1127078;
    drug->drug_exposure_start_date = uk_start_date_;
    drug->drug_exposure_end_date = uk_end_date_;
    drug->drug_type_concept_id = 38000177;
    
    // Test valid UK quantity
    drug->quantity = 30.0;
    drug->days_supply = 30;
    EXPECT_TRUE(drug->validate());
    
    // Test invalid UK quantity
    drug->quantity = -5.0;
    EXPECT_FALSE(drug->validate());
    auto errors = drug->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("quantity") != std::string::npos; }));
    
    // Test invalid UK days supply
    drug->quantity = 30.0;
    drug->days_supply = -10;
    EXPECT_FALSE(drug->validate());
    errors = drug->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("days_supply") != std::string::npos; }));
}

// Test measurement with UK clinical measurements
TEST_F(UKLocaleTest, MeasurementHandlesUKClinicalMeasurementsCorrectly) {
    auto measurement = std::make_unique<Measurement>();
    measurement->measurement_id = 4001;
    measurement->person_id = 123;
    measurement->measurement_concept_id = 3004501; // UK clinical measurement
    measurement->measurement_date = uk_start_date_;
    measurement->measurement_type_concept_id = 44818701; // UK lab result
    measurement->value_as_number = 36.5; // Body temperature in Celsius
    measurement->unit_concept_id = 8653; // Celsius concept
    measurement->unit_source_value = "°C"; // UK temperature unit
    measurement->range_low = 36.0;
    measurement->range_high = 37.5;
    
    EXPECT_TRUE(measurement->validate());
    EXPECT_EQ(measurement->table_name(), "measurement");
    
    auto values = measurement->field_values();
    EXPECT_FALSE(values.empty());
}

// Test measurement validates UK clinical ranges
TEST_F(UKLocaleTest, MeasurementValidatesUKClinicalRangesCorrectly) {
    auto measurement = std::make_unique<Measurement>();
    measurement->measurement_id = 4001;
    measurement->person_id = 123;
    measurement->measurement_concept_id = 3004501;
    measurement->measurement_date = uk_start_date_;
    measurement->measurement_type_concept_id = 44818701;
    
    // Test valid UK clinical range
    measurement->range_low = 36.0; // Normal UK body temperature range
    measurement->range_high = 37.5;
    EXPECT_TRUE(measurement->validate());
    
    // Test invalid UK clinical range
    measurement->range_low = 37.5;
    measurement->range_high = 36.0; // Invalid range
    EXPECT_FALSE(measurement->validate());
    auto errors = measurement->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("range") != std::string::npos; }));
}

// Test note table with UK clinical notes
TEST_F(UKLocaleTest, NoteTableHandlesUKClinicalNotesCorrectly) {
    auto note = std::make_unique<Note>();
    note->note_id = 6001;
    note->person_id = 123;
    note->note_date = uk_start_date_;
    note->note_type_concept_id = 44814637; // UK progress note
    note->note_class_concept_id = 44814639; // UK clinical note
    note->note_title = "Progress Note";
    note->note_text = "Patient reports improvement in symptoms.";
    note->encoding_concept_id = 32678; // UTF-8
    note->language_concept_id = 4180186; // English (UK)
    
    EXPECT_TRUE(note->validate());
    EXPECT_EQ(note->table_name(), "note");
}

// Test note table handles UK large text content efficiently
TEST_F(UKLocaleTest, NoteTableHandlesUKLargeTextContentEfficiently) {
    auto note = std::make_unique<Note>();
    note->note_id = 6001;
    note->person_id = 123;
    note->note_date = uk_start_date_;
    note->note_type_concept_id = 44814637;
    note->note_class_concept_id = 44814639;
    note->note_title = "Comprehensive Assessment";
    note->encoding_concept_id = 32678;
    note->language_concept_id = 4180186;
    
    // Test with large UK clinical note content
    std::string large_text(5000, 'A');
    note->note_text = large_text;
    
    std::string sql = note->to_insert_sql();
    EXPECT_TRUE(sql.length() > 5000);
}

// Test note table handles UK text escaping
TEST_F(UKLocaleTest, NoteTableHandlesUKTextEscapingCorrectly) {
    auto note = std::make_unique<Note>();
    note->note_id = 6001;
    note->person_id = 123;
    note->note_date = uk_start_date_;
    note->note_type_concept_id = 44814637;
    note->note_class_concept_id = 44814639;
    note->note_title = "Patient's Progress Note"; // UK apostrophe usage
    note->note_text = "Patient states: \"I'm feeling better\" and reports no side effects.";
    note->encoding_concept_id = 32678;
    note->language_concept_id = 4180186;
    
    std::string sql = note->to_insert_sql(true);
    EXPECT_TRUE(sql.find("Patient''s Progress Note") != std::string::npos);
    EXPECT_TRUE(sql.find("\"I''m feeling better\"") != std::string::npos);
}

// Test observation with UK clinical observations
TEST_F(UKLocaleTest, ObservationHandlesUKClinicalObservationsCorrectly) {
    auto observation = std::make_unique<Observation>();
    observation->observation_id = 5001;
    observation->person_id = 123;
    observation->observation_concept_id = 4005823; // UK clinical observation
    observation->observation_date = uk_start_date_;
    observation->observation_type_concept_id = 44818701; // UK survey
    observation->value_as_number = 120.5; // Blood pressure in mmHg
    observation->value_as_string = "Positive"; // UK clinical result
    observation->value_as_concept_id = 4188539; // UK concept
    
    EXPECT_TRUE(observation->validate());
    EXPECT_EQ(observation->table_name(), "observation");
    
    std::string sql = observation->to_insert_sql();
    EXPECT_TRUE(sql.find("'Positive'") != std::string::npos);
}

// Test observation period with UK NHS observation periods
TEST_F(UKLocaleTest, ObservationPeriodHandlesUKNHSObservationPeriodsCorrectly) {
    auto obs_period = std::make_unique<ObservationPeriod>();
    obs_period->observation_period_id = 456;
    obs_period->person_id = 123;
    obs_period->observation_period_start_date = uk_start_date_;
    obs_period->observation_period_end_date = uk_end_date_;
    obs_period->period_type_concept_id = 44814722; // UK NHS enrollment
    
    EXPECT_TRUE(obs_period->validate());
    EXPECT_EQ(obs_period->table_name(), "observation_period");
}

// Test observation period handles UK NHS enrollment validation
TEST_F(UKLocaleTest, ObservationPeriodHandlesUKNHSEnrollmentValidationCorrectly) {
    auto obs_period = std::make_unique<ObservationPeriod>();
    obs_period->observation_period_id = 0; // Invalid UK NHS enrollment ID
    obs_period->person_id = 123;
    obs_period->observation_period_start_date = uk_start_date_;
    obs_period->observation_period_end_date = uk_end_date_;
    obs_period->period_type_concept_id = 44814722;
    
    EXPECT_FALSE(obs_period->validate());
    auto errors = obs_period->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("observation_period_id") != std::string::npos; }));
}

// Test observation period validates UK date ranges
TEST_F(UKLocaleTest, ObservationPeriodValidatesUKDateRangesCorrectly) {
    auto obs_period = std::make_unique<ObservationPeriod>();
    obs_period->observation_period_id = 456;
    obs_period->person_id = 123;
    obs_period->observation_period_start_date = uk_start_date_;
    obs_period->observation_period_end_date = uk_start_date_ - hours(24); // Invalid UK date range
    obs_period->period_type_concept_id = 44814722;
    
    EXPECT_FALSE(obs_period->validate());
}

// Test OMOP factory creates all UK supported tables
TEST_F(UKLocaleTest, OMOPFactoryCreatesAllUKSupportedTablesCorrectly) {
    auto person = OmopTableFactory::create("person");
    ASSERT_NE(person, nullptr);
    EXPECT_EQ(person->table_name(), "person");

    auto obs_period = OmopTableFactory::create("observation_period");
    ASSERT_NE(obs_period, nullptr);
    EXPECT_EQ(obs_period->table_name(), "observation_period");

    auto visit = OmopTableFactory::create("visit_occurrence");
    ASSERT_NE(visit, nullptr);
    EXPECT_EQ(visit->table_name(), "visit_occurrence");
}

// Test OMOP factory handles UK edge cases
TEST_F(UKLocaleTest, OMOPFactoryCreateHandlesUKEdgeCasesCorrectly) {
    // Test empty table name
    auto empty_table = OmopTableFactory::create("");
    EXPECT_EQ(empty_table, nullptr);
    
    // Test exception during creation
    OmopTableFactory::register_table("uk_error_table", []() -> std::unique_ptr<OmopTable> {
        throw std::runtime_error("UK creation error");
    });
    
    auto error_table = OmopTableFactory::create("uk_error_table");
    
    // Clean up the error table so it doesnt affect other tests
    OmopTableFactory::unregister_table("uk_error_table");
    EXPECT_EQ(error_table, nullptr);
}

// Test OMOP factory handles UK unsupported tables gracefully
TEST_F(UKLocaleTest, OMOPFactoryHandlesUKUnsupportedTablesGracefully) {
    auto table = OmopTableFactory::create("uk_nonexistent_table");
    EXPECT_EQ(table, nullptr);
}

// Test OMOP factory provides UK basic functionality for all tables
TEST_F(UKLocaleTest, OMOPFactoryProvidesUKBasicFunctionalityForAllTables) {
    auto supported_tables = OmopTableFactory::get_supported_tables();

    for (const auto& table_name : supported_tables) {
        // Skip test-specific error tables that are intentionally broken
        if (table_name.find("error_table") != std::string::npos) {
            continue;
        }
        
        auto table = OmopTableFactory::create(table_name);
        ASSERT_NE(table, nullptr) << "Failed to create UK table: " << table_name;

        // Test basic UK table methods
        EXPECT_EQ(table->table_name(), table_name);
        EXPECT_FALSE(table->field_names().empty()) << "No field names for UK table: " << table_name;

        // Test UK validation result structure
        auto result = table->validate_detailed();
        EXPECT_EQ(result.is_valid, table->validate());
    }
}

// Test OMOP factory provides UK supported table list
TEST_F(UKLocaleTest, OMOPFactoryProvidesUKSupportedTableList) {
    auto tables = OmopTableFactory::get_supported_tables();
    EXPECT_FALSE(tables.empty());
    EXPECT_TRUE(std::find(tables.begin(), tables.end(), "person") != tables.end());
    EXPECT_TRUE(std::find(tables.begin(), tables.end(), "observation_period") != tables.end());
}

// Test OMOP factory registration handles UK custom tables with error cases
TEST_F(UKLocaleTest, OMOPFactoryRegistrationHandlesUKCustomTablesWithErrorCases) {
    // Test empty table name
    EXPECT_THROW(
        OmopTableFactory::register_table("", []() { return std::make_unique<Person>(); }),
        std::invalid_argument
    );
    
    // Test null creator function
    std::function<std::unique_ptr<OmopTable>()> null_creator;
    EXPECT_THROW(
        OmopTableFactory::register_table("uk_test", null_creator),
        std::invalid_argument
    );
}

// Test OMOP factory supports UK custom registration
TEST_F(UKLocaleTest, OMOPTableSupportsUKCustomRegistration) {
    class UKCustomTable : public OmopTable {
    public:
        [[nodiscard]] std::string table_name() const override { return "uk_custom_table"; }
        [[nodiscard]] std::string to_insert_sql(bool = true) const override { return "INSERT INTO uk_custom_table"; }
        [[nodiscard]] std::vector<std::string> field_names() const override { return {"nhs_number"}; }
        [[nodiscard]] std::vector<std::any> field_values() const override { return {std::string("**********")}; }
        void visit_fields(FieldVisitor& visitor) const override {
            visitor.visit("nhs_number", std::string("**********"));
        }
        [[nodiscard]] bool validate() const override { return true; }
        [[nodiscard]] std::vector<std::string> validation_errors() const override { return {}; }
    };
    
    OmopTableFactory::register_table("uk_custom_table", []() { return std::make_unique<UKCustomTable>(); });
    
    EXPECT_TRUE(OmopTableFactory::is_supported("uk_custom_table"));
    auto custom = OmopTableFactory::create("uk_custom_table");
    ASSERT_NE(custom, nullptr);
    EXPECT_EQ(custom->table_name(), "uk_custom_table");
}

// Test OMOP factory validates UK table support
TEST_F(UKLocaleTest, OMOPFactoryValidatesUKTableSupportCorrectly) {
    EXPECT_TRUE(OmopTableFactory::is_supported("person"));
    EXPECT_TRUE(OmopTableFactory::is_supported("observation_period"));
    EXPECT_FALSE(OmopTableFactory::is_supported("uk_nonexistent_table"));
}

// Test OMOP schema handles UK empty table names
TEST_F(UKLocaleTest, OMOPSchemaHandlesUKEmptyTableNamesCorrectly) {
    EXPECT_THROW(
        OmopSchema::get_create_table_sql("", "uk_cdm"),
        std::invalid_argument
    );
    
    EXPECT_THROW(
        OmopSchema::get_table_indexes("", "uk_cdm"),
        std::invalid_argument
    );
    
    EXPECT_THROW(
        OmopSchema::get_foreign_keys("", "uk_cdm"),
        std::invalid_argument
    );
}

// Test OMOP table handles UK schema names
TEST_F(UKLocaleTest, OMOPTablesHandleUKSchemaNamesCorrectly) {
    auto person = std::make_unique<Person>();
    EXPECT_EQ(person->schema_name(), "cdm"); // Default UK schema
    
    auto visit = std::make_unique<VisitOccurrence>();
    EXPECT_EQ(visit->schema_name(), "cdm"); // All UK tables use same schema
}

// Test OMOP table implements all required UK virtual methods
TEST_F(UKLocaleTest, OMOPTableImplementsAllRequiredUKVirtualMethods) {
    auto supported_tables = OmopTableFactory::get_supported_tables();

    for (const auto& table_name : supported_tables) {
        // Skip test-specific error tables that are intentionally broken
        if (table_name.find("error_table") != std::string::npos) {
            continue;
        }
        
        auto table = OmopTableFactory::create(table_name);
        ASSERT_NE(table, nullptr);

        // Test all required UK virtual methods can be called
        EXPECT_NO_THROW(table->table_name());
        EXPECT_NO_THROW(table->to_insert_sql());
        EXPECT_NO_THROW(table->field_names());
        EXPECT_NO_THROW(table->field_values());
        EXPECT_NO_THROW(table->validate());
        EXPECT_NO_THROW(table->validation_errors());
        EXPECT_NO_THROW(table->validate_detailed());
    }
}

// Test OMOP tables provide UK detailed validation results
TEST_F(UKLocaleTest, OMOPTablesProvideUKDetailedValidationResultsCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507;
    person->year_of_birth = 1980;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    auto result = person->validate_detailed();
    EXPECT_TRUE(result.is_valid);
    EXPECT_TRUE(result.errors.empty());
    EXPECT_TRUE(result); // Test implicit bool conversion
    
    // Test invalid UK case
    person->person_id = 0;
    result = person->validate_detailed();
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.errors.empty());
    EXPECT_FALSE(result); // Test implicit bool conversion
}

// Test person table with UK demographics
TEST_F(UKLocaleTest, PersonTableHandlesUKDemographicsCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507; // Male in UK
    person->year_of_birth = 1980;
    person->race_concept_id = 8552; // White (UK Census)
    person->ethnicity_concept_id = 38003564; // Not Hispanic
    person->person_source_value = "NHS-**********"; // UK NHS number
    
    EXPECT_TRUE(person->validate());
    EXPECT_EQ(person->table_name(), "person");
    EXPECT_TRUE(person->validation_errors().empty());
    
    auto names = person->field_names();
    EXPECT_FALSE(names.empty());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "person_id") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "gender_concept_id") != names.end());
}

// Test person table generates UK SQL
TEST_F(UKLocaleTest, PersonTableGeneratesUKSQLCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507;
    person->year_of_birth = 1980;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    person->person_source_value = "NHS-**********";
    
    std::string sql = person->to_insert_sql();
    EXPECT_FALSE(sql.empty());
    EXPECT_TRUE(sql.find("INSERT INTO") != std::string::npos);
    EXPECT_TRUE(sql.find("person") != std::string::npos);
    EXPECT_TRUE(sql.find("NHS-**********") != std::string::npos);
}

// Test person table validates UK birth years
TEST_F(UKLocaleTest, PersonTableValidatesUKBirthYearsCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    // Test valid UK birth year
    person->year_of_birth = 1980;
    EXPECT_TRUE(person->validate());

    // Test invalid UK birth year (too old)
    person->year_of_birth = 1800;
    EXPECT_FALSE(person->validate());

    // Test future UK birth year (should be invalid)
    person->year_of_birth = 2030;
    EXPECT_FALSE(person->validate());
}

// Test person table validates UK required fields
TEST_F(UKLocaleTest, PersonTableValidatesUKRequiredFieldsCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507;
    person->year_of_birth = 1980;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    // Test missing UK required fields
    person->person_id = 0;
    EXPECT_FALSE(person->validate());

    person->person_id = 123;
    person->gender_concept_id = 0;
    EXPECT_FALSE(person->validate());
}

// Test procedure occurrence with UK procedures
TEST_F(UKLocaleTest, ProcedureOccurrenceHandlesUKProceduresCorrectly) {
    auto procedure = std::make_unique<ProcedureOccurrence>();
    procedure->procedure_occurrence_id = 3001;
    procedure->person_id = 123;
    procedure->procedure_concept_id = 4019824; // UK OPCS procedure
    procedure->procedure_date = uk_start_date_;
    procedure->procedure_type_concept_id = 44818517; // UK primary procedure
    procedure->procedure_datetime = uk_start_date_;
    procedure->procedure_end_date = uk_end_date_;
    procedure->procedure_end_datetime = uk_end_date_;
    
    EXPECT_TRUE(procedure->validate());
    EXPECT_EQ(procedure->table_name(), "procedure_occurrence");
    
    std::string sql = procedure->to_insert_sql();
    EXPECT_FALSE(sql.find("NULL") == std::string::npos); // Should have non-NULL datetime values
}

// Test SQL formatting helper handles UK null dates
TEST_F(UKLocaleTest, SQLFormattingHelperHandlesUKNullDatesCorrectly) {
    auto null_time = std::chrono::system_clock::time_point{};
    std::string result = TestOmopTable::format_datetime_sql(null_time);
    EXPECT_EQ(result, "NULL");
}

// Test SQL formatting helper handles UK special characters
TEST_F(UKLocaleTest, SQLFormattingHelperHandlesUKSpecialCharactersCorrectly) {
    EXPECT_EQ(TestOmopTable::escape_string_sql(""), "NULL");
    EXPECT_EQ(TestOmopTable::escape_string_sql("O'Brien"), "'O''Brien'");
    EXPECT_EQ(TestOmopTable::escape_string_sql("C:\\NHS\\path"), "'C:\\\\NHS\\\\path'");
    EXPECT_EQ(TestOmopTable::escape_string_sql("Quote's and \\backslash"), "'Quote''s and \\\\backslash'");
}

// Test SQL injection prevention works for UK data
TEST_F(UKLocaleTest, SQLInjectionPreventionWorksForUKData) {
    auto condition = std::make_unique<ConditionOccurrence>();
    condition->condition_occurrence_id = 1;
    condition->person_id = 1;
    condition->condition_concept_id = 201820;
    condition->condition_start_date = uk_current_time_;
    condition->condition_type_concept_id = 44818517;
    
    // Attempt SQL injection with UK context
    condition->condition_source_value = "'; DROP TABLE uk_person; --";
    condition->stop_reason = "' OR '1'='1";
    
    std::string sql = condition->to_insert_sql(true);
    EXPECT_TRUE(sql.find("''; DROP TABLE uk_person; --'") != std::string::npos);
    EXPECT_TRUE(sql.find("'' OR ''1''=''1'") != std::string::npos);
}

// Test UK optional fields generate NULL values
TEST_F(UKLocaleTest, UKOptionalFieldsGenerateNULLValuesCorrectly) {
    auto person = std::make_unique<Person>();
    person->person_id = 1;
    person->gender_concept_id = 8507;
    person->year_of_birth = 1990;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    // All optional UK fields should be NULL in SQL
    std::string sql = person->to_insert_sql();
    size_t null_count = 0;
    size_t pos = 0;
    while ((pos = sql.find("NULL", pos)) != std::string::npos) {
        null_count++;
        pos += 4;
    }
    EXPECT_GT(null_count, 5); // Many optional UK fields should be NULL
}

// Test visit occurrence handles UK date validation
TEST_F(UKLocaleTest, VisitOccurrenceHandlesUKDateValidationCorrectly) {
    auto visit = std::make_unique<VisitOccurrence>();
    visit->visit_occurrence_id = 789;
    visit->person_id = 123;
    visit->visit_concept_id = 9201;
    visit->visit_start_date = uk_end_date_; // End date before start date (invalid)
    visit->visit_end_date = uk_start_date_;
    visit->visit_type_concept_id = 44818517;
    
    EXPECT_FALSE(visit->validate());
    auto errors = visit->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("visit_start_date") != std::string::npos; }));
}

// Test visit occurrence with UK healthcare visits
TEST_F(UKLocaleTest, VisitOccurrenceHandlesUKHealthcareVisitsCorrectly) {
    auto visit = std::make_unique<VisitOccurrence>();
    visit->visit_occurrence_id = 789;
    visit->person_id = 123;
    visit->visit_concept_id = 9201; // UK outpatient visit
    visit->visit_start_date = uk_start_date_;
    visit->visit_end_date = uk_end_date_;
    visit->visit_type_concept_id = 44818517; // UK EHR record
    visit->care_site_id = 456; // UK NHS hospital
    visit->visit_source_value = "A&E"; // UK Accident & Emergency
    
    EXPECT_TRUE(visit->validate());
    EXPECT_EQ(visit->table_name(), "visit_occurrence");
}

// Test visit occurrence with field visitor handles UK data
TEST_F(UKLocaleTest, VisitOccurrenceWithFieldVisitorHandlesUKDataCorrectly) {
    auto condition = std::make_unique<ConditionOccurrence>();
    condition->condition_occurrence_id = 1001;
    condition->person_id = 123;
    condition->condition_concept_id = 201820;
    condition->condition_start_date = uk_start_date_;
    condition->condition_type_concept_id = 44818517;
    condition->condition_source_value = "M79.3"; // UK ICD-10
    
    class UKTestVisitor : public FieldVisitor {
    public:
        std::vector<std::string> visited_fields;
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back(name);
        }
    };
    
    UKTestVisitor visitor;
    condition->visit_fields(visitor);
    EXPECT_TRUE(std::find(visitor.visited_fields.begin(), visitor.visited_fields.end(), 
                         "condition_occurrence_id") != visitor.visited_fields.end());
}

// Test comprehensive coverage of all factory edge cases
TEST_F(UKLocaleTest, ComprehensiveCoverageOfAllFactoryEdgeCases) {
    auto supported_tables = OmopTableFactory::get_supported_tables();
    
    // Verify each table can be created and has basic functionality
    for (const auto& table_name : supported_tables) {
        if (table_name.find("error_table") != std::string::npos) {
            continue; // Skip test-specific error tables
        }
        
        auto table = OmopTableFactory::create(table_name);
        ASSERT_NE(table, nullptr) << "Failed to create table: " + table_name;
        
        // Test schema name consistency
        EXPECT_EQ(table->schema_name(), "cdm");
        
        // Test table name consistency
        EXPECT_EQ(table->table_name(), table_name);
        
        // Verify field operations work
        auto field_names = table->field_names();
        auto field_values = table->field_values();
        EXPECT_EQ(field_names.size(), field_values.size()) << "Field count mismatch for " + table_name;
        
        // Test visitor pattern
        class TestVisitor : public FieldVisitor {
        public:
            int visit_count = 0;
            void visit(const std::string& name, const std::any& value) override {
                visit_count++;
            }
        };
        
        TestVisitor visitor;
        table->visit_fields(visitor);
        EXPECT_EQ(visitor.visit_count, static_cast<int>(field_names.size())) << "Visitor count mismatch for " + table_name;
        
        // Test validation methods
        bool is_valid = table->validate();
        auto errors = table->validation_errors();
        auto detailed_result = table->validate_detailed();
        
        EXPECT_EQ(is_valid, detailed_result.is_valid) << "Validation inconsistency for " + table_name;
        EXPECT_EQ(is_valid, errors.empty()) << "Validation error inconsistency for " + table_name;
        EXPECT_EQ(detailed_result.errors.size(), errors.size()) << "Error count inconsistency for " + table_name;
        
        // Test SQL generation
        EXPECT_NO_THROW(table->to_insert_sql(true)) << "SQL generation with escaping failed for " + table_name;
        EXPECT_NO_THROW(table->to_insert_sql(false)) << "SQL generation without escaping failed for " + table_name;
    }
}

// Test comprehensive UK postcode validation edge cases
TEST_F(UKLocaleTest, ComprehensiveUKPostcodeValidationEdgeCases) {
    auto location = std::make_unique<Location>();
    location->location_id = 1;
    
    // Test various valid UK postcode formats
    std::vector<std::string> valid_postcodes = {
        "M1 1AA", "M60 1NW", "CR0 2YR", "DN55 1PT", "W1A 0AX", "EC1A 1BB",
        "SW1A 1AA", "B33 8TH", "W1T 3JH", "SE1 9RT", "N1C 4QP", "E14 5JP",
        "GU16 7HF", "L1 8JQ", "S1 2HE", "NE1 4ST", "LS1 5AP", "CV1 5FB"
    };
    
    for (const auto& postcode : valid_postcodes) {
        location->zip = postcode;
        EXPECT_TRUE(location->validate()) << "Valid UK postcode failed validation: " + postcode;
        auto errors = location->validation_errors();
        EXPECT_TRUE(errors.empty()) << "Valid UK postcode generated errors: " + postcode;
    }
    
    // Test invalid UK postcode formats
    std::vector<std::string> invalid_postcodes = {
        "INVALID", "12345", "ABC123", "M1 1AAA", "M1 11A", "M1 1A", 
        "M1-1AA", "M1.1AA", "M11AA", "M 1AA", "   "
    };
    
    for (const auto& postcode : invalid_postcodes) {
        location->zip = postcode;
        EXPECT_FALSE(location->validate()) << "Invalid UK postcode passed validation: " + postcode;
        auto errors = location->validation_errors();
        EXPECT_FALSE(errors.empty()) << "Invalid UK postcode generated no errors: " + postcode;
    }
    
    // Test optional postcode (should be valid)
    location->zip = std::nullopt;
    EXPECT_TRUE(location->validate()) << "Optional UK postcode failed validation";
}

// Test comprehensive UK date validation and formatting
TEST_F(UKLocaleTest, ComprehensiveUKDateValidationAndFormatting) {
    // Test UK date formatting helper
    auto now = system_clock::now();
    std::string uk_date = OmopTable::format_uk_date(now);
    EXPECT_FALSE(uk_date.empty());
    
    // UK date format should be DD/MM/YYYY
    EXPECT_TRUE(uk_date.find('/') != std::string::npos);
    
    // Test null time point
    auto null_time = system_clock::time_point{};
    std::string null_date = OmopTable::format_uk_date(null_time);
    EXPECT_TRUE(null_date.empty());
    
    // Test Person with comprehensive UK birth date validation
    auto person = std::make_unique<Person>();
    person->person_id = 1;
    person->gender_concept_id = 8507;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    // Test leap year validation
    person->year_of_birth = 2000; // Leap year
    person->month_of_birth = 2;   // February
    person->day_of_birth = 29;    // Leap day
    EXPECT_TRUE(person->validate()) << "Leap year February 29 should be valid";
    
    // Test non-leap year validation
    person->year_of_birth = 1999; // Non-leap year
    person->month_of_birth = 2;   // February
    person->day_of_birth = 29;    // Invalid leap day
    EXPECT_FALSE(person->validate()) << "Non-leap year February 29 should be invalid";
    
    // Test February with valid day in non-leap year
    person->day_of_birth = 28;
    EXPECT_TRUE(person->validate()) << "February 28 in non-leap year should be valid";
    
    // Test month-specific day validation
    person->month_of_birth = 4; // April (30 days)
    person->day_of_birth = 31;  // Invalid for April
    EXPECT_FALSE(person->validate()) << "April 31 should be invalid";
    
    person->day_of_birth = 30;  // Valid for April
    EXPECT_TRUE(person->validate()) << "April 30 should be valid";
}

// Test comprehensive UK currency and number formatting edge cases
TEST_F(UKLocaleTest, ComprehensiveUKCurrencyAndNumberFormattingEdgeCases) {
    // Test with large UK currency amounts
    EXPECT_EQ(formatUKCurrency(1234567.89), "£1234567.89");
    EXPECT_EQ(formatUKCurrency(0.01), "£0.01");
    EXPECT_EQ(formatUKCurrency(0.00), "£0.00");
    
    // Test UK temperature formatting
    EXPECT_EQ(formatUKTemperature(36.6), "36.6°C");
    EXPECT_EQ(formatUKTemperature(-10.5), "-10.5°C");
    EXPECT_EQ(formatUKTemperature(100.0), "100.0°C");
    
    // Test measurement with UK units
    auto measurement = std::make_unique<Measurement>();
    measurement->measurement_id = 1;
    measurement->person_id = 123;
    measurement->measurement_concept_id = 3004501;
    measurement->measurement_date = uk_start_date_;
    measurement->measurement_type_concept_id = 44818701;
    measurement->unit_source_value = "°C";
    measurement->value_as_number = 36.6;
    
    EXPECT_TRUE(measurement->validate());
    auto sql = measurement->to_insert_sql();
    EXPECT_TRUE(sql.find("36.6") != std::string::npos);
    EXPECT_TRUE(sql.find("°C") != std::string::npos);
}

// Test comprehensive SQL injection prevention for all UK data types
TEST_F(UKLocaleTest, ComprehensiveSQLInjectionPreventionForAllUKDataTypes) {
    // Test various SQL injection attempts
    std::vector<std::string> injection_attempts = {
        "'; DROP TABLE person; --",
        "' OR '1'='1",
        "'; UPDATE person SET person_id=999; --",
        "' UNION SELECT * FROM person --",
        "'; INSERT INTO person VALUES (999); --",
        "\"; DROP TABLE person; --",
        "\\'; DROP TABLE person; --"
    };
    
    for (const auto& injection : injection_attempts) {
        // Test with Note (text fields)
        auto note = std::make_unique<Note>();
        note->note_id = 1;
        note->person_id = 123;
        note->note_date = uk_start_date_;
        note->note_type_concept_id = 44814637;
        note->note_class_concept_id = 44814639;
        note->note_title = injection;
        note->note_text = injection;
        note->encoding_concept_id = 32678;
        note->language_concept_id = 4180186;
        
        std::string sql = note->to_insert_sql(true);
        // Ensure injection is properly escaped (look for escaped quotes)
        if (injection.find("'") != std::string::npos) {
            std::string escaped_injection = injection;
            size_t pos = 0;
            while ((pos = escaped_injection.find("'", pos)) != std::string::npos) {
                escaped_injection.replace(pos, 1, "''");
                pos += 2;
            }
            EXPECT_TRUE(sql.find(escaped_injection) != std::string::npos) << "SQL injection not properly escaped: " + injection;
        }
        
        // Test with Location (address fields)
        auto location = std::make_unique<Location>();
        location->location_id = 1;
        location->address_1 = injection;
        location->city = injection;
        location->zip = "M1 1AA"; // Valid UK postcode
        
        sql = location->to_insert_sql(true);
        if (injection.find("'") != std::string::npos) {
            std::string escaped_injection = injection;
            size_t pos = 0;
            while ((pos = escaped_injection.find("'", pos)) != std::string::npos) {
                escaped_injection.replace(pos, 1, "''");
                pos += 2;
            }
            EXPECT_TRUE(sql.find(escaped_injection) != std::string::npos) << "SQL injection not properly escaped in location: " + injection;
        }
    }
}

// Test concept table with UK vocabulary standards
TEST_F(UKLocaleTest, ConceptTableHandlesUKVocabularyStandardsCorrectly) {
    auto concept_table = std::make_unique<Concept>();
    concept_table->concept_id = 8507;
    concept_table->concept_name = "Male";
    concept_table->domain_id = "Gender";
    concept_table->vocabulary_id = "Gender";
    concept_table->concept_class_id = "Gender";
    concept_table->concept_code = "M";
    concept_table->valid_start_date = uk_start_date_;
    concept_table->valid_end_date = uk_end_date_;
    concept_table->standard_concept = "S";
    
    EXPECT_TRUE(concept_table->validate());
    EXPECT_EQ(concept_table->table_name(), "concept");
    
    std::string sql = concept_table->to_insert_sql();
    EXPECT_TRUE(sql.find("8507") != std::string::npos);
    EXPECT_TRUE(sql.find("'Male'") != std::string::npos);
    EXPECT_TRUE(sql.find("'Gender'") != std::string::npos);
}

// Test concept table validates UK required fields
TEST_F(UKLocaleTest, ConceptTableValidatesUKRequiredFieldsCorrectly) {
    auto concept_table = std::make_unique<Concept>();
    concept_table->concept_id = 0; // Invalid UK concept ID
    concept_table->concept_name = "";
    concept_table->domain_id = "";
    concept_table->vocabulary_id = "";
    concept_table->concept_class_id = "";
    concept_table->concept_code = "";
    concept_table->valid_start_date = uk_start_date_;
    concept_table->valid_end_date = uk_end_date_;
    
    EXPECT_FALSE(concept_table->validate());
    auto errors = concept_table->validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("concept_id") != std::string::npos; }));
}

// Test care site table with UK NHS facilities
TEST_F(UKLocaleTest, CareSiteTableHandlesUKNHSFacilitiesCorrectly) {
    auto care_site = std::make_unique<CareSite>();
    care_site->care_site_id = 12345;
    care_site->care_site_name = "Royal London Hospital";
    care_site->place_of_service_concept_id = 8756; // UK hospital
    care_site->location_id = 1;
    care_site->care_site_source_value = "RLH-001";
    care_site->place_of_service_source_value = "Hospital";
    
    EXPECT_TRUE(care_site->validate());
    EXPECT_EQ(care_site->table_name(), "care_site");
    
    std::string sql = care_site->to_insert_sql();
    EXPECT_TRUE(sql.find("12345") != std::string::npos);
    EXPECT_TRUE(sql.find("'Royal London Hospital'") != std::string::npos);
    EXPECT_TRUE(sql.find("'RLH-001'") != std::string::npos);
}

// Test care site table validates UK required fields
TEST_F(UKLocaleTest, CareSiteTableValidatesUKRequiredFieldsCorrectly) {
    auto care_site = std::make_unique<CareSite>();
    care_site->care_site_id = 0; // Invalid UK care site ID
    
    EXPECT_FALSE(care_site->validate());
    auto errors = care_site->validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("care_site_id") != std::string::npos; }));
}

// Test provider table with UK medical professionals
TEST_F(UKLocaleTest, ProviderTableHandlesUKMedicalProfessionalsCorrectly) {
    auto provider = std::make_unique<Provider>();
    provider->provider_id = 67890;
    provider->provider_name = "Dr. Sarah Johnson";
    provider->npi = "UK123456789"; // UK healthcare provider identifier
    provider->specialty_concept_id = 38004446; // UK General Practice
    provider->care_site_id = 12345;
    provider->year_of_birth = 1975;
    provider->gender_concept_id = 8532; // Female
    provider->provider_source_value = "GP-001";
    provider->specialty_source_value = "General Practice";
    provider->gender_source_value = "Female";
    
    EXPECT_TRUE(provider->validate());
    EXPECT_EQ(provider->table_name(), "provider");
    
    std::string sql = provider->to_insert_sql();
    EXPECT_TRUE(sql.find("67890") != std::string::npos);
    EXPECT_TRUE(sql.find("'Dr. Sarah Johnson'") != std::string::npos);
    EXPECT_TRUE(sql.find("'UK123456789'") != std::string::npos);
    EXPECT_TRUE(sql.find("'GP-001'") != std::string::npos);
}

// Test provider table validates UK required fields
TEST_F(UKLocaleTest, ProviderTableValidatesUKRequiredFieldsCorrectly) {
    auto provider = std::make_unique<Provider>();
    provider->provider_id = 0; // Invalid UK provider ID
    
    EXPECT_FALSE(provider->validate());
    auto errors = provider->validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("provider_id") != std::string::npos; }));
}

// Test visit detail table with UK healthcare visit components
TEST_F(UKLocaleTest, VisitDetailTableHandlesUKHealthcareVisitComponentsCorrectly) {
    auto visit_detail = std::make_unique<VisitDetail>();
    visit_detail->visit_detail_id = 98765;
    visit_detail->person_id = 123;
    visit_detail->visit_detail_concept_id = 9201; // UK outpatient visit
    visit_detail->visit_detail_start_date = uk_start_date_;
    visit_detail->visit_detail_end_date = uk_end_date_;
    visit_detail->visit_detail_type_concept_id = 44818517; // UK EHR record
    visit_detail->visit_occurrence_id = 789;
    visit_detail->provider_id = 67890;
    visit_detail->care_site_id = 12345;
    visit_detail->visit_detail_source_value = "OutpatientConsultation";
    
    EXPECT_TRUE(visit_detail->validate());
    EXPECT_EQ(visit_detail->table_name(), "visit_detail");
    
    std::string sql = visit_detail->to_insert_sql();
    EXPECT_TRUE(sql.find("98765") != std::string::npos);
    EXPECT_TRUE(sql.find("789") != std::string::npos);
    EXPECT_TRUE(sql.find("'OutpatientConsultation'") != std::string::npos);
}

// Test visit detail table validates UK required fields
TEST_F(UKLocaleTest, VisitDetailTableValidatesUKRequiredFieldsCorrectly) {
    auto visit_detail = std::make_unique<VisitDetail>();
    visit_detail->visit_detail_id = 0; // Invalid UK visit detail ID
    visit_detail->person_id = 123;
    visit_detail->visit_detail_concept_id = 9201;
    visit_detail->visit_detail_start_date = uk_start_date_;
    visit_detail->visit_detail_end_date = uk_end_date_;
    visit_detail->visit_detail_type_concept_id = 44818517;
    visit_detail->visit_occurrence_id = 789;
    
    EXPECT_FALSE(visit_detail->validate());
    auto errors = visit_detail->validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("visit_detail_id") != std::string::npos; }));
}

// Test visit detail table validates UK date ranges
TEST_F(UKLocaleTest, VisitDetailTableValidatesUKDateRangesCorrectly) {
    auto visit_detail = std::make_unique<VisitDetail>();
    visit_detail->visit_detail_id = 98765;
    visit_detail->person_id = 123;
    visit_detail->visit_detail_concept_id = 9201;
    visit_detail->visit_detail_start_date = uk_end_date_; // End date before start date (invalid)
    visit_detail->visit_detail_end_date = uk_start_date_;
    visit_detail->visit_detail_type_concept_id = 44818517;
    visit_detail->visit_occurrence_id = 789;
    
    EXPECT_FALSE(visit_detail->validate());
    auto errors = visit_detail->validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(),
        [](const std::string& e) { return e.find("visit_detail_start_date") != std::string::npos; }));
}

// Test comprehensive UK table factory coverage for all tables
TEST_F(UKLocaleTest, ComprehensiveUKTableFactoryCoverageForAllTables) {
    // Test all UK table types can be created through factory
    std::vector<std::string> expected_tables = {
        "person", "observation_period", "visit_occurrence", "condition_occurrence",
        "drug_exposure", "procedure_occurrence", "measurement", "observation",
        "death", "note", "concept", "location", "care_site", "provider", "visit_detail"
    };
    
    for (const auto& table_name : expected_tables) {
        auto table = OmopTableFactory::create(table_name);
        ASSERT_NE(table, nullptr) << "Failed to create UK table: " + table_name;
        EXPECT_EQ(table->table_name(), table_name);
        
        // Test UK table has proper field structure
        auto field_names = table->field_names();
        auto field_values = table->field_values();
        EXPECT_EQ(field_names.size(), field_values.size()) << "Field mismatch for UK table: " + table_name;
        
        // Test UK table validation methods
        EXPECT_NO_THROW(table->validate()) << "Validation failed for UK table: " + table_name;
        EXPECT_NO_THROW(table->validation_errors()) << "Validation errors failed for UK table: " + table_name;
        
        // Test UK table SQL generation
        EXPECT_NO_THROW(table->to_insert_sql()) << "SQL generation failed for UK table: " + table_name;
    }
}

// Tests thread-safe concurrent access to UK table factory
TEST_F(UKLocaleTest, ConcurrentUKTableFactoryAccessIsThreadSafe) {
    const int num_threads = 10;
    const int iterations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    auto worker = [&success_count, iterations_per_thread]() {
        for (int i = 0; i < iterations_per_thread; ++i) {
            // Test concurrent table creation
            auto person = OmopTableFactory::create("person");
            if (person && person->table_name() == "person") {
                success_count++;
            }
            
            // Test concurrent supported check
            if (OmopTableFactory::is_supported("observation_period")) {
                auto obs = OmopTableFactory::create("observation_period");
                if (obs) success_count++;
            }
            
            // Test concurrent table list access
            auto tables = OmopTableFactory::get_supported_tables();
            if (!tables.empty()) {
                success_count++;
            }
        }
    };
    
    // Launch threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(worker);
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    // Verify all operations succeeded
    EXPECT_EQ(success_count.load(), num_threads * iterations_per_thread * 3);
}

// Tests Person table with all edge cases for UK date validation
TEST_F(UKLocaleTest, PersonTableValidatesAllUKDateEdgeCases) {
    auto person = std::make_unique<Person>();
    person->person_id = 123;
    person->gender_concept_id = 8507;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    
    // Test all months with appropriate days
    std::vector<std::pair<int, int>> month_days = {
        {1, 31}, {2, 28}, {3, 31}, {4, 30}, {5, 31}, {6, 30},
        {7, 31}, {8, 31}, {9, 30}, {10, 31}, {11, 30}, {12, 31}
    };
    
    person->year_of_birth = 2023; // Non-leap year
    for (const auto& [month, max_day] : month_days) {
        person->month_of_birth = month;
        person->day_of_birth = max_day;
        EXPECT_TRUE(person->validate()) << "Failed for month " << month << " day " << max_day;
        
        // Test invalid day for this month
        person->day_of_birth = max_day + 1;
        EXPECT_FALSE(person->validate()) << "Should fail for month " << month << " day " << (max_day + 1);
    }
    
    // Test century leap year (2000)
    person->year_of_birth = 2000;
    person->month_of_birth = 2;
    person->day_of_birth = 29;
    EXPECT_TRUE(person->validate()) << "Century leap year February 29 should be valid";
    
    // Test non-century 100-year (1900)
    person->year_of_birth = 1900;
    person->month_of_birth = 2;
    person->day_of_birth = 29;
    EXPECT_FALSE(person->validate()) << "1900 February 29 should be invalid";
}

// Tests all UK table types with completely empty optional fields
TEST_F(UKLocaleTest, AllUKTablesHandleEmptyOptionalFieldsCorrectly) {
    // Test Person with minimal fields
    auto person = std::make_unique<Person>();
    person->person_id = 1;
    person->gender_concept_id = 8507;
    person->year_of_birth = 1990;
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    // All optional fields left as nullopt
    EXPECT_TRUE(person->validate());
    std::string sql = person->to_insert_sql();
    EXPECT_TRUE(sql.find("INSERT INTO") != std::string::npos);
    
    // Test DrugExposure with minimal fields
    auto drug = std::make_unique<DrugExposure>();
    drug->drug_exposure_id = 1;
    drug->person_id = 1;
    drug->drug_concept_id = 1127078;
    drug->drug_exposure_start_date = uk_start_date_;
    drug->drug_exposure_end_date = uk_end_date_;
    drug->drug_type_concept_id = 38000177;
    // All optional fields left as nullopt
    EXPECT_TRUE(drug->validate());
    
    // Test Measurement with minimal fields
    auto measurement = std::make_unique<Measurement>();
    measurement->measurement_id = 1;
    measurement->person_id = 1;
    measurement->measurement_concept_id = 3004501;
    measurement->measurement_date = uk_start_date_;
    measurement->measurement_type_concept_id = 44818701;
    // All optional fields left as nullopt
    EXPECT_TRUE(measurement->validate());
}

// Tests extreme values for UK numeric fields
TEST_F(UKLocaleTest, UKTablesHandleExtremeNumericValuesCorrectly) {
    // Test Person with extreme year
    auto person = std::make_unique<Person>();
    person->person_id = std::numeric_limits<int64_t>::max();
    person->gender_concept_id = 8507;
    person->year_of_birth = 1850; // Minimum valid year
    person->race_concept_id = 8552;
    person->ethnicity_concept_id = 38003564;
    EXPECT_TRUE(person->validate());
    
    // Test Measurement with extreme values
    auto measurement = std::make_unique<Measurement>();
    measurement->measurement_id = std::numeric_limits<int64_t>::max();
    measurement->person_id = 1;
    measurement->measurement_concept_id = 3004501;
    measurement->measurement_date = uk_start_date_;
    measurement->measurement_type_concept_id = 44818701;
    measurement->value_as_number = std::numeric_limits<double>::max();
    measurement->range_low = std::numeric_limits<double>::lowest();
    measurement->range_high = std::numeric_limits<double>::max();
    EXPECT_TRUE(measurement->validate());
    
    // Test DrugExposure with extreme quantities
    auto drug = std::make_unique<DrugExposure>();
    drug->drug_exposure_id = 1;
    drug->person_id = 1;
    drug->drug_concept_id = 1127078;
    drug->drug_exposure_start_date = uk_start_date_;
    drug->drug_exposure_end_date = uk_end_date_;
    drug->drug_type_concept_id = 38000177;
    drug->quantity = 999999.99;
    drug->days_supply = 365 * 10; // 10 years supply
    EXPECT_TRUE(drug->validate());
}

// Tests UK postcode validation with additional edge cases
TEST_F(UKLocaleTest, UKPostcodeValidationHandlesAdditionalEdgeCases) {
    auto location = std::make_unique<Location>();
    location->location_id = 1;
    
    // Test postcodes with lowercase (should be converted to uppercase internally)
    location->zip = "sw1a 1aa";
    EXPECT_TRUE(location->validate()) << "Lowercase postcode should be valid";
    
    // Test postcodes with extra spaces
    location->zip = "SW1A  1AA";
    EXPECT_TRUE(location->validate()) << "Postcode with extra spaces should be valid";
    
    // Test postcodes without space (should be valid if space can be inferred)
    location->zip = "SW1A1AA";
    EXPECT_TRUE(location->validate()) << "Postcode without space should be valid";
    
    // Test invalid character combinations
    location->zip = "QW1A 1AA"; // Q not allowed as first character
    EXPECT_FALSE(location->validate()) << "Postcode with Q as first char should be invalid";
    
    location->zip = "WI1A 1AA"; // I not allowed as second character
    EXPECT_FALSE(location->validate()) << "Postcode with I as second char should be invalid";
    
    location->zip = "W1C 1AA"; // C not allowed in third position
    EXPECT_FALSE(location->validate()) << "Postcode with C in third position should be invalid";
}

} // namespace omop::cdm::test