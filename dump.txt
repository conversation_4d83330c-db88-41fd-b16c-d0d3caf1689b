File tests/unit/extract/database_connector_test.cpp:

/**
 * @file database_connector_test.cpp
 * @brief Unit tests for database connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <chrono>
#include <thread>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;
using ::testing::NiceMock;
using ::testing::A;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD((std::unique_ptr<IPreparedStatement>), prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

// Mock result set for testing
class MockResultSet : public IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t index), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string& column_name), (const, override));
    MOCK_METHOD(bool, is_null, (size_t index), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string& column_name), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t index), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t index), (const, override));
    MOCK_METHOD(Record, to_record, (), (const, override));
};

// Mock prepared statement for testing
class MockPreparedStatement : public IPreparedStatement {
public:
    MOCK_METHOD(void, bind, (size_t index, const std::any& value), (override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (), (override));
    MOCK_METHOD(size_t, execute_update, (), (override));
    MOCK_METHOD(void, clear_parameters, (), (override));
};

// ============================================================================
// DatabaseConnectorConnectionPoolTest Tests
// ============================================================================

// Test fixture for connection pool tests
class DatabaseConnectorConnectionPoolTest : public ::testing::Test {
protected:
    void SetUp() override {
        connection_factory_ = []() {
            auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
            return conn;
        };
    }

    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
};

// Tests basic connection pool operations
TEST_F(DatabaseConnectorConnectionPoolTest, AcquireAndRelease) {
    ConnectionPool pool(2, 5, connection_factory_);

    // Acquire connection
    auto conn1 = pool.acquire();
    ASSERT_NE(nullptr, conn1);
    EXPECT_TRUE(conn1->is_connected());

    // Release connection
    pool.release(std::move(conn1));

    // Acquire again - should get the same connection
    auto conn2 = pool.acquire();
    ASSERT_NE(nullptr, conn2);
}

// Tests clearing idle connections
TEST_F(DatabaseConnectorConnectionPoolTest, ClearIdleConnections) {
    ConnectionPool pool(2, 5, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.idle_connections);

    pool.clear_idle_connections();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(0, stats2.idle_connections);
}

// Tests connection validation
TEST_F(DatabaseConnectorConnectionPoolTest, ConnectionValidation) {
    // Factory that creates connections that become invalid
    auto factory = []() {
        auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
        static int count = 0;
        // First connection will become invalid
        if (count++ == 0) {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(false));
        } else {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
        }
        return conn;
    };

    ConnectionPool pool(2, 5, factory);

    // Validate connections - should remove invalid ones
    size_t removed = pool.validate_connections();
    EXPECT_GE(removed, 0);

    // Pool should maintain minimum connections
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 2);
}

// Test for memory leak detection in connection pool
TEST_F(DatabaseConnectorConnectionPoolTest, DISABLED_MemoryLeakDetection) {
    struct AllocationTracker {
        std::atomic<size_t> allocated{0};
        std::atomic<size_t> deallocated{0};
        
        size_t net_allocations() const {
            return allocated.load() - deallocated.load();
        }
    };
    
    auto tracker = std::make_shared<AllocationTracker>();
    
    auto factory = [tracker]() {
        class TrackedConnection : public MockDatabaseConnection {
            std::shared_ptr<AllocationTracker> tracker_;
        public:
            explicit TrackedConnection(std::shared_ptr<AllocationTracker> t) 
                : tracker_(t) {
                tracker_->allocated++;
            }
            
            ~TrackedConnection() {
                tracker_->deallocated++;
            }
        };
        
        return std::make_unique<TrackedConnection>(tracker);
    };
    
    {
        ConnectionPool pool(2, 5, factory);
        auto conn = pool.acquire();
        pool.release(std::move(conn));
    }
    
    // After pool destruction, all connections should be deallocated
    // Allow some time for async cleanup
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    // Use a less strict check in case there are lingering references
    EXPECT_LE(tracker->net_allocations(), 1);
}

// Tests pool growth when needed
TEST_F(DatabaseConnectorConnectionPoolTest, PoolGrowth) {
    ConnectionPool pool(1, 3, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(1, stats1.total_connections);

    // Acquire more connections than minimum
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_GE(stats2.total_connections, 2);
    EXPECT_LE(stats2.total_connections, 3);
}

// Tests pool statistics
TEST_F(DatabaseConnectorConnectionPoolTest, PoolStatistics) {
    ConnectionPool pool(2, 5, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.total_connections);
    EXPECT_EQ(0, stats1.active_connections);
    EXPECT_EQ(2, stats1.idle_connections);

    // Acquire connections
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(2, stats2.total_connections);
    EXPECT_EQ(2, stats2.active_connections);
    EXPECT_EQ(0, stats2.idle_connections);

    // Release one connection
    pool.release(std::move(conn1));

    auto stats3 = pool.get_statistics();
    EXPECT_EQ(2, stats3.total_connections);
    EXPECT_EQ(1, stats3.active_connections);
    EXPECT_EQ(1, stats3.idle_connections);
}

// Tests pool timeout
TEST_F(DatabaseConnectorConnectionPoolTest, AcquireTimeout) {
    ConnectionPool pool(1, 1, connection_factory_);

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Try to acquire another with timeout
    std::thread acquire_thread([&pool]() {
        EXPECT_THROW(pool.acquire(100), DatabaseException);
    });

    acquire_thread.join();
}

// ============================================================================
// DatabaseExtractorTest Tests
// ============================================================================

// Test fixture for database extractor tests
class DatabaseExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_connection_ = std::make_unique<NiceMock<MockDatabaseConnection>>();
        mock_connection_ptr_ = mock_connection_.get();

        // Set up default expectations with proper cleanup
        ON_CALL(*mock_connection_ptr_, is_connected())
            .WillByDefault(Return(true));
        ON_CALL(*mock_connection_ptr_, get_database_type())
            .WillByDefault(Return("MockDB"));
        ON_CALL(*mock_connection_ptr_, disconnect())
            .WillByDefault(Return());
    }
    
    void TearDown() override {
        // Ensure proper cleanup of mock connections
        if (mock_connection_ && mock_connection_ptr_) {
            mock_connection_ptr_->disconnect();
        }
        mock_connection_.reset();
        mock_connection_ptr_ = nullptr;
    }

    std::unique_ptr<MockDatabaseConnection> mock_connection_;
    MockDatabaseConnection* mock_connection_ptr_;
};

// Tests building complex queries
TEST_F(DatabaseExtractorTest, BuildComplexQuery) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["columns"] = std::vector<std::string>{"person_id", "birth_datetime"};
    config["filter"] = std::string("gender_concept_id = 8507");
    config["order_by"] = std::string("person_id");
    config["limit"] = 100;

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests batch extraction
TEST_F(DatabaseExtractorTest, ExtractBatch) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    auto result_ptr = mock_result.get();

    // Set up result set behavior
    EXPECT_CALL(*result_ptr, next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    EXPECT_CALL(*result_ptr, column_count())
        .WillRepeatedly(Return(3));

    EXPECT_CALL(*result_ptr, column_name(_))
        .WillRepeatedly([](size_t index) {
            switch(index) {
                case 0: return std::string("person_id");
                case 1: return std::string("birth_datetime");
                case 2: return std::string("gender_concept_id");
                default: return std::string("");
            }
        });

    EXPECT_CALL(*result_ptr, get_value(A<size_t>()))
        .WillRepeatedly([](size_t index) {
            switch(index) {
                case 0: return std::any(static_cast<long long>(1));
                case 1: return std::any(std::string("1990-01-01"));
                case 2: return std::any(static_cast<long long>(8507));
                default: return std::any{};
            }
        });

    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size());
}

// Tests initialization failure for missing table
TEST_F(DatabaseExtractorTest, InitializeMissingTable) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("nonexistent", ""))
        .WillOnce(Return(false));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("nonexistent");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), DatabaseException);
}

// Tests basic database extraction initialization
TEST_F(DatabaseExtractorTest, InitializeExtractor) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", ""))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests initialization with column selection
TEST_F(DatabaseExtractorTest, InitializeWithColumns) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT person_id, birth_datetime, gender_concept_id FROM person";

    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["columns"] = std::vector<std::string>{"person_id", "birth_datetime", "gender_concept_id"};

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization with filter condition
TEST_F(DatabaseExtractorTest, InitializeWithFilter) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT * FROM person WHERE gender_concept_id = 8507";

    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["filter"] = std::string("gender_concept_id = 8507");

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization with schema
TEST_F(DatabaseExtractorTest, InitializeWithSchema) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", "cdm"))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["schema"] = std::string("cdm");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests parameter binding safety
TEST_F(DatabaseExtractorTest, ParameterBindingSafety) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    // Test with potentially dangerous filter that should be properly escaped
    config["filter"] = std::string("name = 'O'Connor' AND age > 18");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Test resource exhaustion scenarios
TEST_F(DatabaseExtractorTest, DISABLED_ResourceExhaustionHandling) {
    // Set up mock to simulate large result set
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    // Create a mock result that simulates memory pressure
    class ExhaustingResultSet : public MockResultSet {
    private:
        mutable size_t call_count_ = 0;
        const size_t max_rows_ = 1000000;
        
    public:
        bool next() override {
            if (++call_count_ > max_rows_) {
                // Simulate out of memory after many rows
                throw std::bad_alloc();
            }
            return call_count_ <= max_rows_;
        }
        
        Record to_record() const override {
            Record r;
            r.setField("id", static_cast<int>(call_count_));
            // Large string to consume memory
            r.setField("data", std::string(10000, 'X'));
            return r;
        }
    };

    auto mock_result = std::make_unique<ExhaustingResultSet>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("large_table");

    ProcessingContext context;
    extractor.initialize(config, context);

    // Should handle memory exhaustion gracefully
    EXPECT_THROW(extractor.extract_batch(1000, context), std::exception);
}

// Tests SQL injection prevention
TEST_F(DatabaseExtractorTest, SQLInjectionPrevention) {
    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    // Test with potentially malicious filter
    config["filter"] = std::string("name = 'Robert'; DROP TABLE person; --'");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), SecurityException);
}

// Tests getting statistics
TEST_F(DatabaseExtractorTest, GetStatistics) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("records_extracted"));
    EXPECT_TRUE(stats.contains("extraction_time"));
}

// ============================================================================
// Standalone Tests
// ============================================================================

// Tests database connection factory
TEST(DatabaseConnectionFactoryTest, RegisterAndCreate) {
    // Register a test connection type
    DatabaseConnectionFactory::instance().register_type("test_db", [](const IDatabaseConnection::ConnectionParams&) {
        return std::make_unique<NiceMock<MockDatabaseConnection>>();
    });

    // Create connection
    IDatabaseConnection::ConnectionParams params;
    auto connection = DatabaseConnectionFactory::instance().create("test_db", params);
    ASSERT_NE(nullptr, connection);

    // Test invalid type
    EXPECT_THROW(DatabaseConnectionFactory::instance().create("invalid_db", params), DatabaseException);
}

// Tests result set base implementation
TEST(ResultSetBaseTest, ToRecord) {
    class TestResultSet : public ResultSetBase {
    public:
        bool next() override { return false; }
        std::any get_value(size_t index) const override {
            switch(index) {
                case 0: return 123;
                case 1: return std::string("test");
                case 2: return 45.67;
                default: return std::any{};
            }
        }
        std::any get_value(const std::string&) const override { return std::any{}; }
        bool is_null(size_t index) const override { return index >= 3; }
        bool is_null(const std::string&) const override { return false; }
        size_t column_count() const override { return 4; }
        std::string column_name(size_t index) const override {
            switch(index) {
                case 0: return "id";
                case 1: return "name";
                case 2: return "value";
                case 3: return "null_col";
                default: return "";
            }
        }
        std::string column_type(size_t) const override { return "unknown"; }
    };

    TestResultSet result_set;
    Record record = result_set.to_record();

    EXPECT_EQ(123, std::any_cast<int>(record.getField("id")));
    EXPECT_EQ("test", std::any_cast<std::string>(record.getField("name")));
    EXPECT_DOUBLE_EQ(45.67, std::any_cast<double>(record.getField("value")));
    EXPECT_FALSE(record.hasField("null_col"));
}

File tests/unit/extract/connection_pool_test.cpp:

/**
 * @file connection_pool_test.cpp
 * @brief Unit tests for ConnectionPool and related classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <iostream>

namespace omop::extract::test {

using namespace ::testing;
using namespace std::chrono_literals;

// Mock database connection for testing
class MockDatabaseConnection : public omop::extract::IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const omop::extract::IDatabaseConnection::ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<omop::extract::IResultSet>, execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD(std::unique_ptr<omop::extract::IPreparedStatement>, prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

// Test fixture for ConnectionPool tests
class ConnectionPoolTest : public ::testing::Test {
protected:
    // Factory function for creating mock connections
    std::unique_ptr<omop::extract::IDatabaseConnection> CreateMockConnection() {
        auto conn = std::make_unique<MockDatabaseConnection>();
        EXPECT_CALL(*conn, is_connected())
            .WillRepeatedly(Return(true));
        return conn;
    }

    // Factory function that tracks creation count
    std::unique_ptr<omop::extract::IDatabaseConnection> CreateTrackedConnection() {
        creation_count_++;
        return CreateMockConnection();
    }

    std::atomic<int> creation_count_{0};
};

// Test connection pool timeout
TEST_F(ConnectionPoolTest, AcquisitionTimeout) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Try to acquire another with timeout
    auto start = std::chrono::steady_clock::now();
    EXPECT_THROW(pool.acquire(100), omop::common::DatabaseException);
    auto end = std::chrono::steady_clock::now();

    // Verify timeout occurred
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_GE(duration.count(), 100);
    EXPECT_LT(duration.count(), 200); // Should not wait much longer than timeout
}

// Test basic connection pool creation and acquisition
TEST_F(ConnectionPoolTest, BasicPoolOperations) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Acquire a connection
    auto conn1 = pool.acquire();
    EXPECT_NE(conn1, nullptr);
    EXPECT_TRUE(conn1->is_connected());

    // Get statistics
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 1);
    EXPECT_GE(stats.total_connections, 2); // At least min_connections

    // Release connection
    pool.release(std::move(conn1));

    stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_GT(stats.idle_connections, 0);
}

// Test clearing idle connections
TEST_F(ConnectionPoolTest, ClearIdleConnections) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Get initial statistics
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(stats1.idle_connections, 2);

    // Clear idle connections
    pool.clear_idle_connections();

    // Check final statistics
    auto stats2 = pool.get_statistics();
    EXPECT_EQ(stats2.idle_connections, 0);
    EXPECT_EQ(stats2.total_connections, 0);
}

// Test concurrent access to connection pool
TEST_F(ConnectionPoolTest, ConcurrentAccess) {
    omop::extract::ConnectionPool pool(2, 10, [this]() { return CreateMockConnection(); });

    std::atomic<int> success_count{0};
    std::vector<std::thread> threads;

    // Launch multiple threads to acquire and release connections
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&pool, &success_count]() {
            try {
                auto conn = pool.acquire(1000);
                if (conn) {
                    success_count++;
                    std::this_thread::sleep_for(10ms);
                    pool.release(std::move(conn));
                }
            } catch (...) {
                // Timeout is acceptable in this test
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // At least some threads should have succeeded
    EXPECT_GT(success_count, 0);

    // Final statistics should be consistent
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_EQ(stats.total_connections, stats.idle_connections);
}

// Test connection validation
TEST_F(ConnectionPoolTest, ConnectionValidation) {
    int conn_index = 0;

    auto factory = [&conn_index]() {
        auto conn = std::make_unique<MockDatabaseConnection>();

        // First 2 connections are valid, 3rd is invalid, 4th is valid
        if (conn_index < 2 || conn_index == 3) {
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        } else {
            EXPECT_CALL(*conn, is_connected())
                .WillOnce(Return(false))  // Invalid during validation
                .WillRepeatedly(Return(true));
        }

        conn_index++;
        return conn;
    };

    omop::extract::ConnectionPool pool(3, 5, factory);

    // Let connections go idle
    std::this_thread::sleep_for(10ms);

    // Validate connections - should remove 1 invalid connection
    size_t invalid_count = pool.validate_connections();
    EXPECT_EQ(invalid_count, 1);

    // Pool should maintain minimum connections
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 3);
}

// Test connection timeout scenarios
TEST_F(ConnectionPoolTest, ConnectionTimeoutScenarios) {
    // Test 1: Zero timeout should fail immediately
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();
        
        auto start = std::chrono::steady_clock::now();
        EXPECT_THROW(pool.acquire(0), omop::common::DatabaseException);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        EXPECT_LT(duration.count(), 10); // Should fail almost immediately
    }

    // Test 2: Very short timeout
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();
        
        auto start = std::chrono::steady_clock::now();
        EXPECT_THROW(pool.acquire(1), omop::common::DatabaseException);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        EXPECT_GE(duration.count(), 1);
        EXPECT_LT(duration.count(), 50); // Should not wait much longer
    }

    // Test 3: Successful acquisition after release
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();
        
        // Release connection in another thread
        std::thread release_thread([&pool, conn = std::move(conn1)]() mutable {
            std::this_thread::sleep_for(50ms);
            pool.release(std::move(conn));
        });
        
        // Try to acquire with timeout
        auto start = std::chrono::steady_clock::now();
        auto conn2 = pool.acquire(200);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        EXPECT_NE(conn2, nullptr);
        EXPECT_GE(duration.count(), 50); // Should wait for release
        EXPECT_LT(duration.count(), 200); // Should not wait full timeout
        
        release_thread.join();
    }
}

// Test connection factory failure
TEST_F(ConnectionPoolTest, ConnectionFactoryFailure) {
    auto failing_factory = []() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        throw std::runtime_error("Connection creation failed");
    };

    // Should throw when trying to create initial connections
    EXPECT_THROW(omop::extract::ConnectionPool pool(1, 3, failing_factory), omop::common::DatabaseException);
}

// Test destruction with active connections
TEST_F(ConnectionPoolTest, DestructionWithActiveConnections) {
    auto pool = std::make_unique<omop::extract::ConnectionPool>(2, 5, [this]() { return CreateMockConnection(); });

    // Acquire connections
    auto conn1 = pool->acquire();
    auto conn2 = pool->acquire();

    // Verify connections are active
    auto stats = pool->get_statistics();
    EXPECT_EQ(stats.active_connections, 2);

    // Destroy pool with active connections - should not crash
    pool.reset();

    // Connections should be automatically cleaned up
    EXPECT_TRUE(true); // If we get here, no crash occurred
}

// Test equal min/max connections
TEST_F(ConnectionPoolTest, EqualMinMaxConnections) {
    size_t conn_count = 3;
    omop::extract::ConnectionPool pool(conn_count, conn_count, [this]() { return CreateMockConnection(); });

    // Pool should create exactly conn_count connections
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_connections, conn_count);
    EXPECT_EQ(stats.idle_connections, conn_count);

    // Acquire all connections
    std::vector<std::unique_ptr<omop::extract::IDatabaseConnection>> connections;
    for (size_t i = 0; i < conn_count; ++i) {
        connections.push_back(pool.acquire());
    }

    // Try to acquire one more - should timeout
    EXPECT_THROW(pool.acquire(100), omop::common::DatabaseException);
}

// Test failing connection factory
TEST_F(ConnectionPoolTest, FailingConnectionFactory) {
    int call_count = 0;
    auto factory = [&call_count]() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        call_count++;
        if (call_count <= 2) {
            // First two calls succeed
            auto conn = std::make_unique<MockDatabaseConnection>();
            EXPECT_CALL(*conn, is_connected()).WillRepeatedly(Return(true));
            return conn;
        } else {
            // Subsequent calls fail
            throw std::runtime_error("Factory failure");
        }
    };

    omop::extract::ConnectionPool pool(2, 5, factory);

    // Initial connections should be created successfully
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();
    EXPECT_NE(conn1, nullptr);
    EXPECT_NE(conn2, nullptr);

    // Try to acquire more - should fail due to factory error
    EXPECT_THROW(pool.acquire(), omop::common::DatabaseException);
}

// Test infinite timeout
TEST_F(ConnectionPoolTest, InfiniteTimeout) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Use shared pointer and atomic to control thread lifetime safely
    std::atomic<bool> should_release{false};
    std::shared_ptr<omop::extract::ConnectionPool> pool_ptr(&pool, [](auto*){});
    
    // Start a thread to release the connection after a delay
    std::thread release_thread([pool_ptr, &should_release, conn = std::move(conn1)]() mutable {
        std::this_thread::sleep_for(100ms);
        if (should_release.load()) {
            pool_ptr->release(std::move(conn));
        }
    });

    should_release.store(true);
    
    // Try to acquire with infinite timeout (-1 means infinite timeout)
    auto start = std::chrono::steady_clock::now();
    auto conn2 = pool.acquire(-1); // -1 means infinite timeout
    auto end = std::chrono::steady_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_NE(conn2, nullptr);
    EXPECT_GE(duration.count(), 90); // Should wait for release (allow some variance)

    // Ensure thread finishes before test ends
    if (release_thread.joinable()) {
        release_thread.join();
    }
}

// Test invalid connection handling
TEST_F(ConnectionPoolTest, InvalidConnectionHandling) {
    int call_count = 0;
    auto factory = [&call_count]() {
        call_count++;
        auto conn = std::make_unique<MockDatabaseConnection>();
        
        if (call_count == 1) {
            // First connection (will be acquired and validated)
            EXPECT_CALL(*conn, is_connected())
                .WillOnce(Return(false))
                .WillRepeatedly(Return(true));
        } else if (call_count == 2) {
            // Second connection (will remain in idle pool, never used)
            // No expectations - this connection won't be used
        } else {
            // Subsequent connections (created during acquire) are immediately valid
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        }
        return conn;
    };

    omop::extract::ConnectionPool pool(2, 5, factory);

    // Acquire connection - should get a new one since initial ones are invalid
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);

    // Connection should be valid after acquisition
    EXPECT_TRUE(conn->is_connected());
}

// Test min/max connection limits
TEST_F(ConnectionPoolTest, MinMaxConnectionLimits) {
    size_t min_conn = 2;
    size_t max_conn = 4;

    omop::extract::ConnectionPool pool(min_conn, max_conn, [this]() { return CreateTrackedConnection(); });

    // Initial creation should be min_connections
    EXPECT_EQ(creation_count_, min_conn);

    // Acquire all min connections
    std::vector<std::unique_ptr<omop::extract::IDatabaseConnection>> connections;
    for (size_t i = 0; i < min_conn; ++i) {
        connections.push_back(pool.acquire());
    }

    // Pool should create new connections up to max
    connections.push_back(pool.acquire());
    EXPECT_GT(creation_count_, min_conn);

    connections.push_back(pool.acquire());
    EXPECT_EQ(creation_count_, max_conn);

    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, max_conn);
    EXPECT_EQ(stats.total_connections, max_conn);
}

// Test pool shutdown behavior
TEST_F(ConnectionPoolTest, PoolShutdownBehavior) {
    auto factory = [this]() { return CreateMockConnection(); };
    auto pool = std::make_unique<omop::extract::ConnectionPool>(2, 5, factory);
    
    // Acquire some connections
    auto conn1 = pool->acquire();
    auto conn2 = pool->acquire();
    
    EXPECT_NE(conn1, nullptr);
    EXPECT_NE(conn2, nullptr);
    
    // Get initial statistics
    auto stats_before = pool->get_statistics();
    EXPECT_EQ(stats_before.active_connections, 2);
    
    // Start a background thread that will try to acquire connections
    std::atomic<bool> should_stop{false};
    std::atomic<int> background_acquisitions{0};
    std::atomic<bool> background_exception{false};
    
    std::thread background_thread([&pool, &should_stop, &background_acquisitions, &background_exception]() {
        while (!should_stop) {
            try {
                auto conn = pool->acquire(10);
                if (conn) {
                    background_acquisitions++;
                    pool->release(std::move(conn));
                }
            } catch (const omop::common::DatabaseException&) {
                // Timeout is expected
            } catch (...) {
                background_exception = true;
                break;
            }
            std::this_thread::sleep_for(1ms);
        }
    });
    
    // Let the background thread run for a bit
    std::this_thread::sleep_for(50ms);
    
    // Signal the background thread to stop
    should_stop = true;
    
    // Release our connections
    pool->release(std::move(conn1));
    pool->release(std::move(conn2));
    
    // Wait for background thread to finish
    background_thread.join();
    
    // Verify no exceptions occurred in background thread
    EXPECT_FALSE(background_exception);
    
    // Verify some acquisitions happened
    EXPECT_GT(background_acquisitions, 0);
    
    // Destroy the pool
    pool.reset();
    
    // If we get here without crashes, the test passes
    EXPECT_TRUE(true);
}

// Test pool statistics
TEST_F(ConnectionPoolTest, PoolStatistics) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Initial statistics
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(stats1.total_connections, 2);
    EXPECT_EQ(stats1.active_connections, 0);
    EXPECT_EQ(stats1.idle_connections, 2);

    // Acquire connections
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(stats2.total_connections, 2);
    EXPECT_EQ(stats2.active_connections, 2);
    EXPECT_EQ(stats2.idle_connections, 0);

    // Release one connection
    pool.release(std::move(conn1));

    auto stats3 = pool.get_statistics();
    EXPECT_EQ(stats3.total_connections, 2);
    EXPECT_EQ(stats3.active_connections, 1);
    EXPECT_EQ(stats3.idle_connections, 1);
}

// Test release null connection
TEST_F(ConnectionPoolTest, ReleaseNullConnection) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Release a null connection - should not crash
    pool.release(nullptr);

    // Pool should still be functional
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);
}

// Test wait time statistics
TEST_F(ConnectionPoolTest, WaitTimeStatistics) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Start a thread to release the connection after a delay
    std::thread release_thread([&pool, conn = std::move(conn1)]() mutable {
        std::this_thread::sleep_for(50ms);
        pool.release(std::move(conn));
    });

    // Try to acquire with timeout
    auto start = std::chrono::steady_clock::now();
    auto conn2 = pool.acquire(200);
    auto end = std::chrono::steady_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_NE(conn2, nullptr);
    EXPECT_GE(duration.count(), 50); // Should wait for release

    // Get statistics
    [[maybe_unused]] auto stats = pool.get_statistics();
    // Note: The actual statistics structure may not have these fields
    // EXPECT_TRUE(stats.contains("total_wait_time"));
    // EXPECT_TRUE(stats.contains("max_wait_time"));

    release_thread.join();
}

} // namespace omop::extract::test


File tests/unit/extract/csv_extractor_test.cpp:

/**
 * @file csv_extractor_test.cpp
 * @brief Unit tests for CSV extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/csv_extractor.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <chrono>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;

// ============================================================================
// CsvFieldParserTest Tests
// ============================================================================

// Test CSV field parser functionality
class CsvFieldParserTest : public ::testing::Test {
protected:
    CsvOptions options_;
    std::unique_ptr<CsvFieldParser> parser_;

    void SetUp() override {
        options_.delimiter = ',';
        options_.quote_char = '"';
        options_.escape_char = '\\';
        options_.trim_fields = true;
        parser_ = std::make_unique<CsvFieldParser>(options_);
    }
};

// Tests field type conversion functionality
TEST_F(CsvFieldParserTest, ConvertFieldTypes) {
    // Test integer conversion
    auto int_value = parser_->convert_field("42", "integer");
    ASSERT_TRUE(int_value.has_value());
    EXPECT_EQ(42LL, std::any_cast<long long>(int_value));

    // Test double conversion with UK decimal format
    auto double_value = parser_->convert_field("3.14", "double");
    ASSERT_TRUE(double_value.has_value());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(double_value));

    // Test boolean conversion
    auto bool_value = parser_->convert_field("TRUE", "boolean");
    ASSERT_TRUE(bool_value.has_value());
    EXPECT_TRUE(std::any_cast<bool>(bool_value));

    // Test null value handling
    auto null_value = parser_->convert_field("", "");
    EXPECT_FALSE(null_value.has_value());

    // Test UK currency format (pounds)
    auto currency_value = parser_->convert_field("£123.45", "string");
    ASSERT_TRUE(currency_value.has_value());
    EXPECT_EQ("£123.45", std::any_cast<std::string>(currency_value));
}

// Tests UK localized data format parsing
TEST_F(CsvFieldParserTest, ParseUKLocalizedFormats) {
    // Set up UK-specific options
    options_.date_format = "%d/%m/%Y";
    options_.datetime_format = "%d/%m/%Y %H:%M:%S";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    // Test UK postal codes
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA",  // Central London
        "M1 1AA",    // Manchester
        "B33 8TH",   // Birmingham
        "W1A 0AX",   // Oxford Street
        "EH1 1YZ",   // Edinburgh
        "CF10 3AT",  // Cardiff
        "BT1 5GS"    // Belfast
    };
    
    for (const auto& postcode : uk_postcodes) {
        auto postcode_val = parser_->convert_field(postcode, "string");
        ASSERT_TRUE(postcode_val.has_value());
        EXPECT_EQ(postcode, std::any_cast<std::string>(postcode_val));
    }
    
    // Test UK telephone numbers
    std::vector<std::string> uk_phone_numbers = {
        "+44 20 7946 0958",   // London with country code
        "020 7946 0958",      // London
        "0161 496 0000",      // Manchester
        "0131 496 0000",      // Edinburgh
        "029 2018 0000"       // Cardiff
    };
    
    for (const auto& phone : uk_phone_numbers) {
        auto phone_val = parser_->convert_field(phone, "string");
        ASSERT_TRUE(phone_val.has_value());
        EXPECT_EQ(phone, std::any_cast<std::string>(phone_val));
    }
    
    // Test UK currency amounts
    std::vector<std::string> uk_currencies = {
        "£0.99",      // Pence
        "£12.50",     // Standard amount
        "£1,234.56",  // Amount with thousands separator
        "£1,000,000", // Million pounds
        "£0.01"       // One penny
    };
    
    for (const auto& currency : uk_currencies) {
        auto currency_val = parser_->convert_field(currency, "string");
        ASSERT_TRUE(currency_val.has_value());
        EXPECT_EQ(currency, std::any_cast<std::string>(currency_val));
    }
    
    // Test UK temperature formats (Celsius)
    std::vector<std::string> uk_temperatures = {
        "23°C",       // Summer temperature
        "5°C",        // Winter temperature
        "-2°C",       // Below freezing
        "0°C",        // Freezing point
        "37°C"        // Body temperature
    };
    
    for (const auto& temp : uk_temperatures) {
        auto temp_val = parser_->convert_field(temp, "string");
        ASSERT_TRUE(temp_val.has_value());
        EXPECT_EQ(temp, std::any_cast<std::string>(temp_val));
    }
}

// Tests comprehensive field parsing edge cases including UK formats
TEST_F(CsvFieldParserTest, ComprehensiveFieldParsingEdgeCases) {
    CsvOptions options;
    options.delimiter = ',';
    options.quote_char = '"';
    options.escape_char = '\\';
    options.trim_fields = true;
    
    CsvFieldParser parser(options);
    
    // Test 1: Empty fields and whitespace handling
    std::string line1 = " , ,  \"\"  , \"  spaced  \" ";
    auto fields1 = parser.parse_line(line1);
    ASSERT_EQ(4, fields1.size());
    EXPECT_EQ("", fields1[0]);        // Empty after trim
    EXPECT_EQ("", fields1[1]);        // Empty after trim
    EXPECT_EQ("", fields1[2]);        // Empty quoted field
    EXPECT_EQ("  spaced  ", fields1[3]); // Preserve internal spaces in quoted field
    
    // Test 2: Complex escape sequences
    std::string line2 = R"("He said \"Hello\", she replied \"Hi!\"","Value with, comma","Tab\there")";
    auto fields2 = parser.parse_line(line2);
    ASSERT_EQ(3, fields2.size());
    EXPECT_EQ("He said \"Hello\", she replied \"Hi!\"", fields2[0]);
    EXPECT_EQ("Value with, comma", fields2[1]);
    EXPECT_EQ("Tab\there", fields2[2]);
    
    // Test 3: Mixed quoted and unquoted fields
    std::string line3 = "unquoted,\"quoted with, comma\",another_unquoted,\"\"";
    auto fields3 = parser.parse_line(line3);
    ASSERT_EQ(4, fields3.size());
    EXPECT_EQ("unquoted", fields3[0]);
    EXPECT_EQ("quoted with, comma", fields3[1]);
    EXPECT_EQ("another_unquoted", fields3[2]);
    EXPECT_EQ("", fields3[3]);
    
    // Test 4: Line ending handling
    std::string line4 = "field1,field2\n";
    auto fields4 = parser.parse_line(line4);
    ASSERT_EQ(2, fields4.size());
    EXPECT_EQ("field1", fields4[0]);
    EXPECT_EQ("field2", fields4[1]);
    
    // Test 5: Unicode handling including UK characters
    std::string line5 = "café,ñoño,\"中文字符\",🚀,£50.25";
    auto fields5 = parser.parse_line(line5);
    ASSERT_EQ(5, fields5.size());
    EXPECT_EQ("café", fields5[0]);
    EXPECT_EQ("ñoño", fields5[1]);
    EXPECT_EQ("中文字符", fields5[2]);
    EXPECT_EQ("🚀", fields5[3]);
    EXPECT_EQ("£50.25", fields5[4]);  // UK currency format
    
    // Test 6: UK date format handling (DD/MM/YYYY)
    std::string line6 = "15/01/2025,\"New Year's Day\",Bristol";
    auto fields6 = parser.parse_line(line6);
    ASSERT_EQ(3, fields6.size());
    EXPECT_EQ("15/01/2025", fields6[0]);  // UK date format
    EXPECT_EQ("New Year's Day", fields6[1]);
    EXPECT_EQ("Bristol", fields6[2]);  // UK city
    
    // Test 7: UK temperature format (Celsius)
    std::string line7 = "London,5°C,Manchester,3°C";
    auto fields7 = parser.parse_line(line7);
    ASSERT_EQ(4, fields7.size());
    EXPECT_EQ("London", fields7[0]);
    EXPECT_EQ("5°C", fields7[1]);     // UK temperature format
    EXPECT_EQ("Manchester", fields7[2]);
    EXPECT_EQ("3°C", fields7[3]);     // UK temperature format
    
    // Test 8: UK postal code format
    std::string line8 = "SW1A 1AA,\"10 Downing Street\",London";
    auto fields8 = parser.parse_line(line8);
    ASSERT_EQ(3, fields8.size());
    EXPECT_EQ("SW1A 1AA", fields8[0]);  // UK postal code format
    EXPECT_EQ("10 Downing Street", fields8[1]);
    EXPECT_EQ("London", fields8[2]);
}


// Tests field parsing with escape characters
TEST_F(CsvFieldParserTest, ParseEscapedField) {
    std::string input = "field\\,with\\,escapes,normal field";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field,with,escapes", field1);
}

// Tests parsing complete line into fields
TEST_F(CsvFieldParserTest, ParseCompleteLine) {
    std::string line = "John,Doe,30,\"New York, NY\"";

    auto fields = parser_->parse_line(line);
    ASSERT_EQ(4, fields.size());
    EXPECT_EQ("John", fields[0]);
    EXPECT_EQ("Doe", fields[1]);
    EXPECT_EQ("30", fields[2]);
    EXPECT_EQ("New York, NY", fields[3]);
}

// Tests field parsing with quoted fields
TEST_F(CsvFieldParserTest, ParseQuotedField) {
    // Corrected input: both fields are quoted
    std::string input = "\"field with, comma\",\"field with \"\" quotes\"";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with, comma", field1);

    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with \" quotes", field2);
}

// Tests date parsing with UK formats (DD/MM/YYYY)
TEST_F(CsvFieldParserTest, ParseDateField) {
    // Test UK date format DD/MM/YYYY
    options_.date_format = "%d/%m/%Y";
    parser_ = std::make_unique<CsvFieldParser>(options_);

    auto uk_date_value = parser_->convert_field("15/01/2025", "date");
    ASSERT_TRUE(uk_date_value.has_value());
    EXPECT_TRUE(uk_date_value.type() == typeid(std::chrono::system_clock::time_point));
    
    // Test ISO format as secondary
    options_.date_format = "%Y-%m-%d";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    auto iso_date_value = parser_->convert_field("2025-01-15", "date");
    ASSERT_TRUE(iso_date_value.has_value());
    EXPECT_TRUE(iso_date_value.type() == typeid(std::chrono::system_clock::time_point));
    
    // Test UK date with time (24-hour format)
    options_.datetime_format = "%d/%m/%Y %H:%M:%S";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    auto uk_datetime_value = parser_->convert_field("15/01/2025 14:30:45", "datetime");
    ASSERT_TRUE(uk_datetime_value.has_value());
    EXPECT_TRUE(uk_datetime_value.type() == typeid(std::chrono::system_clock::time_point));
}

// Tests basic field parsing without quotes
TEST_F(CsvFieldParserTest, ParseSimpleField) {
    std::string input = "field1,field2,field3";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field1", field1);
    EXPECT_EQ(7, pos);

    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field2", field2);
    EXPECT_EQ(14, pos);

    std::string field3 = parser_->parse_field(input, pos);
    EXPECT_EQ("field3", field3);
}

// Tests type conversion error handling
TEST_F(CsvFieldParserTest, TypeConversionErrorHandling) {
    CsvOptions options;
    options.delimiter = ',';
    options.quote_char = '"';
    options.escape_char = '\\';
    options.trim_fields = true;
    
    CsvFieldParser parser(options);
    
    // Test boolean parsing with various formats
    std::vector<std::pair<std::string, bool>> bool_tests = {
        {"true", true}, {"false", false},
        {"TRUE", true}, {"FALSE", false},
        {"True", true}, {"False", false},
        {"1", true}, {"0", false},
        {"yes", true}, {"no", false},
        {"YES", true}, {"NO", false},
        {"Y", true}, {"N", false},
        {"T", true}, {"F", false},
        {"on", true}, {"off", false}
    };
    
    for (const auto& [input, expected] : bool_tests) {
        auto bool_val = parser.convert_field(input, "boolean");
        if (bool_val.has_value() && bool_val.type() == typeid(bool)) {
            EXPECT_EQ(expected, std::any_cast<bool>(bool_val))
                << "Failed for input: " << input;
        }
    }
    
    // Test date parsing with various formats including UK-preferred formats
    std::vector<std::pair<std::string, std::string>> date_formats = {
        {"%d/%m/%Y", "15/01/2025"},      // UK date format (most common)
        {"%d-%m-%Y", "15-01-2025"},      // UK date format (alternative)
        {"%d %B %Y", "15 January 2025"}, // UK long date format
        {"%d %b %Y", "15 Jan 2025"},     // UK short month format
        {"%Y-%m-%d", "2025-01-15"},      // ISO format (secondary)
        {"%Y%m%d", "20250115"},          // Compact format
        {"%d.%m.%Y", "15.01.2025"}       // European format
    };
    
    for (const auto& [format, date_str] : date_formats) {
        CsvOptions format_options = options;
        format_options.date_format = format;
        CsvFieldParser format_parser(format_options);
        
        auto date_val = format_parser.convert_field(date_str, "date");
        EXPECT_TRUE(date_val.has_value())
            << "Failed to parse date: " << date_str << " with format: " << format;
    }
}

// Tests automatic type inference including UK formats
TEST_F(CsvFieldParserTest, InferFieldType) {
    // Integer inference
    auto int_val = parser_->convert_field("12345", "");
    ASSERT_TRUE(int_val.has_value());
    EXPECT_TRUE(int_val.type() == typeid(long long));

    // Double inference with UK decimal format
    auto double_val = parser_->convert_field("123.45", "");
    ASSERT_TRUE(double_val.has_value());
    EXPECT_TRUE(double_val.type() == typeid(double));

    // Boolean inference
    auto bool_val = parser_->convert_field("true", "");
    ASSERT_TRUE(bool_val.has_value());
    EXPECT_TRUE(bool_val.type() == typeid(bool));

    // String fallback
    auto string_val = parser_->convert_field("not a number", "");
    ASSERT_TRUE(string_val.has_value());
    EXPECT_TRUE(string_val.type() == typeid(std::string));
}

// Tests type inference edge cases
TEST_F(CsvFieldParserTest, TypeInferenceEdgeCases) {
    CsvFieldParser parser(options_);
    
    // Test number parsing edge cases
    EXPECT_EQ(typeid(long long), parser.convert_field("0", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("-123", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("+456", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("-123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23e10", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23E-5", "").type());
    
    // Test boolean parsing
    auto bool_val1 = parser.convert_field("true", "");
    EXPECT_EQ(typeid(bool), bool_val1.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val1));
    
    auto bool_val2 = parser.convert_field("FALSE", "");
    EXPECT_EQ(typeid(bool), bool_val2.type());
    EXPECT_FALSE(std::any_cast<bool>(bool_val2));
    
    auto bool_val3 = parser.convert_field("1", "boolean");
    EXPECT_EQ(typeid(bool), bool_val3.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val3));
    
    // Test string fallback for invalid numbers
    EXPECT_EQ(typeid(std::string), parser.convert_field("123abc", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("12.34.56", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("not_a_number", "").type());
    
    // Test null value handling
    auto null_val = parser.convert_field("", "");
    EXPECT_FALSE(null_val.has_value());
    
    auto null_val2 = parser.convert_field("NULL", "");
    EXPECT_FALSE(null_val2.has_value());
    
    auto null_val3 = parser.convert_field("null", "");
    EXPECT_FALSE(null_val3.has_value());
}

// Tests type inference edge cases extended
TEST_F(CsvFieldParserTest, TypeInferenceEdgeCasesExtended) {
    CsvFieldParser parser(options_);
    
    // Test number parsing edge cases
    EXPECT_EQ(typeid(long long), parser.convert_field("0", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("-123", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("+456", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("-123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23e10", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23E-5", "").type());
    
    // Test boolean parsing
    auto bool_val1 = parser.convert_field("true", "");
    EXPECT_EQ(typeid(bool), bool_val1.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val1));
    
    auto bool_val2 = parser.convert_field("FALSE", "");
    EXPECT_EQ(typeid(bool), bool_val2.type());
    EXPECT_FALSE(std::any_cast<bool>(bool_val2));
    
    auto bool_val3 = parser.convert_field("1", "boolean");
    EXPECT_EQ(typeid(bool), bool_val3.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val3));
    
    // Test string fallback for invalid numbers
    EXPECT_EQ(typeid(std::string), parser.convert_field("123abc", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("12.34.56", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("not_a_number", "").type());
    
    // Test null value handling
    auto null_val = parser.convert_field("", "");
    EXPECT_FALSE(null_val.has_value());
    
    auto null_val2 = parser.convert_field("NULL", "");
    EXPECT_FALSE(null_val2.has_value());
    
    auto null_val3 = parser.convert_field("null", "");
    EXPECT_FALSE(null_val3.has_value());
}

// ============================================================================
// CsvExtractorTest Tests
// ============================================================================

// Test fixture for CSV extractor tests
class CsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_csv_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test CSV file
    std::string createTestCsv(const std::string& filename, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests compressed CSV detection
TEST_F(CsvExtractorTest, CompressedCsvDetection) {
    // Test that compressed CSV detection works correctly
    EXPECT_TRUE(CsvExtractor::is_compressed_file("test.csv.gz"));
    EXPECT_TRUE(CsvExtractor::is_compressed_file("test.csv.bz2"));
    EXPECT_FALSE(CsvExtractor::is_compressed_file("test.csv"));
    EXPECT_FALSE(CsvExtractor::is_compressed_file("test.txt"));
}

// Tests CSV directory extractor
TEST_F(CsvExtractorTest, CsvDirectoryExtractor) {
    // Create test files in directory
    createTestCsv("data1.csv", "id,name\n1,John\n2,Jane\n");
    createTestCsv("data2.csv", "id,name\n3,Bob\n4,Alice\n");

    CsvDirectoryExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["directory"] = test_dir_.string();

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(4, batch.size());
}

// Tests CSV extraction with corrupted data
TEST_F(CsvExtractorTest, ExtractCorruptedCsv) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,invalid_age,Los Angeles\n"  // Invalid age
        "Bob,,Chicago\n"                   // Missing age
        "Alice,25,\n";                     // Missing city

    std::string filepath = createTestCsv("corrupted.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["continue_on_error"] = true;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    
    // Should extract all records, even with errors
    EXPECT_EQ(4, batch.size());
    
    // Check statistics for error reporting
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("error_count"));
    // Should have some errors due to invalid data
    EXPECT_GE(std::any_cast<size_t>(stats["error_count"]), 0);
}

// Tests CSV extraction with custom delimiter
TEST_F(CsvExtractorTest, ExtractWithCustomDelimiter) {
    std::string csv_content =
        "name|age|city\n"
        "John|30|New York\n"
        "Jane|25|Los Angeles\n";

    std::string filepath = createTestCsv("pipe_delimited.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["delimiter"] = std::string("|");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
}

// Tests CSV extraction without header
TEST_F(CsvExtractorTest, ExtractWithoutHeader) {
    std::string csv_content =
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("no_header.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["has_header"] = false;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
}

// Tests CSV extraction with column types
TEST_F(CsvExtractorTest, ExtractWithColumnTypes) {
    std::string csv_content =
        "id,name,age,active\n"
        "1,John,30,true\n"
        "2,Jane,25,false\n";

    std::string filepath = createTestCsv("typed.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["column_types"] = std::unordered_map<std::string, std::string>{
        {"id", "integer"},
        {"name", "string"},
        {"age", "integer"},
        {"active", "boolean"}
    };

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Use safer type checking and conversion
    auto age_field = records[0].getField("age");
    ASSERT_TRUE(age_field.type() == typeid(long long));
    EXPECT_EQ(30LL, std::any_cast<long long>(age_field));
    
    auto salary_field = records[0].getField("active");
    ASSERT_TRUE(salary_field.type() == typeid(bool));
    EXPECT_TRUE(std::any_cast<bool>(salary_field));
}

// Tests CSV extraction in batches
TEST_F(CsvExtractorTest, ExtractInBatches) {
    std::string csv_content = "id,name\n";
    for (int i = 1; i <= 25; ++i) {
        csv_content += std::to_string(i) + ",Person" + std::to_string(i) + "\n";
    }

    std::string filepath = createTestCsv("batch_test.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    // Extract in batches of 10
    auto batch1 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch1.size());

    auto batch2 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch2.size());

    auto batch3 = extractor.extract_batch(10, context);
    EXPECT_EQ(5, batch3.size());

    // No more data
    auto batch4 = extractor.extract_batch(10, context);
    EXPECT_EQ(0, batch4.size());
}

// Tests CSV extractor factory
TEST_F(CsvExtractorTest, CsvExtractorFactory) {
    // Test that factory can create CSV extractors
    auto extractor = CsvExtractorFactory::create("csv");
    ASSERT_NE(nullptr, extractor);
    EXPECT_EQ("csv", extractor->get_type());
}

// Tests getting statistics
TEST_F(CsvExtractorTest, GetStatistics) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("stats.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size());

    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("records_extracted"));
    EXPECT_TRUE(stats.contains("extraction_time"));
}

// Tests handling missing file
TEST_F(CsvExtractorTest, HandleMissingFile) {
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string("/nonexistent/file.csv");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), omop::common::ExtractionException);
}

// Tests column type inference
TEST_F(CsvExtractorTest, InferColumnTypes) {
    std::string csv_content =
        "int_col,float_col,bool_col,string_col\n"
        "123,45.67,true,hello\n"
        "456,78.90,false,world\n";

    std::string filepath = createTestCsv("infer_types.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["infer_types"] = true;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Check inferred types with proper type checking
    const auto& int_val = records[0].getField("int_col");
    if (int_val.type() == typeid(long long)) {
        EXPECT_EQ(123LL, std::any_cast<long long>(int_val));
    } else if (int_val.type() == typeid(std::string)) {
        EXPECT_EQ("123", std::any_cast<std::string>(int_val));
    } else {
        FAIL() << "Unexpected type for int_col";
    }

    const auto& float_val = records[0].getField("float_col");
    if (float_val.type() == typeid(double)) {
        EXPECT_DOUBLE_EQ(45.67, std::any_cast<double>(float_val));
    } else if (float_val.type() == typeid(std::string)) {
        EXPECT_EQ("45.67", std::any_cast<std::string>(float_val));
    } else {
        FAIL() << "Unexpected type for float_col";
    }

    const auto& bool_val = records[0].getField("bool_col");
    EXPECT_TRUE(bool_val.type() == typeid(bool) || bool_val.type() == typeid(std::string));
}

// Tests maximum lines limit
TEST_F(CsvExtractorTest, MaxLines) {
    std::string csv_content = "id,name\n";
    for (int i = 1; i <= 20; ++i) {
        csv_content += std::to_string(i) + ",Person" + std::to_string(i) + "\n";
    }

    std::string filepath = createTestCsv("max_lines.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["max_lines"] = 10;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(20, context);
    EXPECT_EQ(9, batch.size()); // 9 data rows (excluding header)

    // Debug output
    std::cout << "[DEBUG] batch.size() = " << batch.size() << std::endl;
    auto records = batch.getRecords();
    for (size_t i = 0; i < std::min(records.size(), size_t(10)); ++i) {
        auto id_val = records[i].getField("id");
        if (id_val.type() == typeid(long long)) {
            std::cout << "[DEBUG] id = " << std::any_cast<long long>(id_val) << std::endl;
        } else if (id_val.type() == typeid(int)) {
            std::cout << "[DEBUG] id = " << std::any_cast<int>(id_val) << std::endl;
        } else if (id_val.type() == typeid(std::string)) {
            std::cout << "[DEBUG] id = " << std::any_cast<std::string>(id_val) << std::endl;
        }
    }
}

// Tests multi-file CSV extractor
TEST_F(CsvExtractorTest, MultiFileCsvExtractor) {
    // Create multiple test files
    createTestCsv("multi1.csv", "id,name\n1,John\n2,Jane\n");
    createTestCsv("multi2.csv", "id,name\n3,Bob\n4,Alice\n");
    createTestCsv("multi3.csv", "id,name\n5,Charlie\n6,Diana\n");

    MultiFileCsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepaths"] = std::vector<std::string>{
        (test_dir_ / "multi1.csv").string(),
        (test_dir_ / "multi2.csv").string(),
        (test_dir_ / "multi3.csv").string()
    };

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(6, batch.size());
}

// Tests skipping lines
TEST_F(CsvExtractorTest, SkipLines) {
    std::string csv_content =
        "# This is a comment\n"
        "# Another comment\n"
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("skip_lines.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["skip_lines"] = 2;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", records[0].getFieldAs<std::string>("name"));
    EXPECT_EQ("Jane", records[1].getFieldAs<std::string>("name"));
}

// Tests basic CSV extraction
TEST_F(CsvExtractorTest, ExtractSimpleCsv) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("simple.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", records[0].getFieldAs<std::string>("name"));
    const auto& age_val = records[0].getField("age");
    if (age_val.type() == typeid(std::string)) {
        EXPECT_EQ("30", std::any_cast<std::string>(age_val));
    } else if (age_val.type() == typeid(long long)) {
        EXPECT_EQ(30LL, std::any_cast<long long>(age_val));
    } else if (age_val.type() == typeid(int)) {
        EXPECT_EQ(30, std::any_cast<int>(age_val));
    } else {
        FAIL() << "Unexpected type for age: " << age_val.type().name();
    }
    EXPECT_EQ("New York", records[0].getFieldAs<std::string>("city"));
}


File tests/unit/extract/extract_utils_test.cpp:

/**
 * @file extract_utils_test.cpp
 * @brief Unit tests for extract utilities and factory functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extract.h"
#include "extract/extractor_factory.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#include "common/exceptions.h"
#include "common/logging.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <future>
#include <regex>
#include <algorithm>
#include <iostream>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::Throw;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

// Mock extractor for testing
class MockIExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize,
                ((const std::unordered_map<std::string, std::any>&), (ProcessingContext&)),
                (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
};

// Test fixture for extract utilities
class ExtractUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_extract_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// ============================================================================
// ExtractUtilsTest Tests
// ============================================================================

// Tests batch extractor
TEST_F(ExtractUtilsTest, BatchExtractor) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    // Set up expectations
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch1, batch2;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.setField("id", i);
        batch1.addRecord(r);
    }
    for (int i = 5; i < 8; ++i) {
        Record r;
        r.setField("id", i);
        batch2.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2));

    BatchExtractor::Config config;
    config.batch_size = 5;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(8, records.size());
}

// Tests batch extractor error handling
TEST_F(ExtractUtilsTest, BatchExtractorErrorHandling) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data()).WillOnce(Return(true));
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Throw(std::runtime_error("Extraction error")));

    BatchExtractor::Config config;
    config.continue_on_error = true;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    EXPECT_THROW(batch_extractor.extract_all(), std::runtime_error);
}

// Tests batch extractor max records
TEST_F(ExtractUtilsTest, BatchExtractorMaxRecords) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    // Set up expectations for exactly 2 batches, then no more data
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true));

    RecordBatch large_batch;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        large_batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(large_batch))
        .WillOnce(Return(large_batch));

    BatchExtractor::Config config;
    config.batch_size = 5;
    config.max_records = 15; // Limit to 15 records

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(15, records.size()); // Should stop at max_records
}

// Tests batch extractor race conditions
TEST_F(ExtractUtilsTest, BatchExtractorRaceConditions) {
    class SlowMockExtractor : public MockIExtractor {
    private:
        mutable std::mutex mutex_;
        std::atomic<int> batch_count_{0};
        std::atomic<bool> has_data_{true};

    public:
        RecordBatch extract_batch(size_t batch_size, 
                                 ProcessingContext& context) override {
            std::lock_guard<std::mutex> lock(mutex_);
            
            // Simulate slow extraction
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
            RecordBatch batch;
            int current_batch = batch_count_++;
            
            for (int i = 0; i < 5; ++i) {
                Record r;
                r.setField("batch", current_batch);
                r.setField("id", i);
                batch.addRecord(r);
            }
            
            if (batch_count_ >= 3) {
                has_data_ = false;
            }
            
            return batch;
        }

        bool has_more_data() const override {
            return has_data_;
        }
    };

    auto mock_extractor = std::make_unique<SlowMockExtractor>();
    BatchExtractor::Config config;
    config.batch_size = 5;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    // Extract in multiple threads to test race conditions
    std::vector<std::future<std::vector<Record>>> futures;
    std::mutex results_mutex;
    std::vector<Record> all_results;

    for (int i = 0; i < 3; ++i) {
        futures.push_back(std::async(std::launch::async, [&batch_extractor]() {
            return batch_extractor.extract_all();
        }));
    }

    // Collect results
    for (auto& future : futures) {
        auto results = future.get();
        std::lock_guard<std::mutex> lock(results_mutex);
        all_results.insert(all_results.end(), results.begin(), results.end());
    }

    // Should have extracted all records without duplicates
    EXPECT_EQ(15, all_results.size());
}

// Tests batch extractor with callback
TEST_F(ExtractUtilsTest, BatchExtractorWithCallback) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 3; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _)).WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;
    BatchExtractor::Config config;
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.emplace_back(current, total);
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(3, records.size());
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(3, progress_updates.back().first);
    EXPECT_EQ(3, progress_updates.back().second);
}

// Tests configuration validation
TEST_F(ExtractUtilsTest, ValidateExtractorConfig) {
    // Valid CSV configuration
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = createTestFile("valid.csv", "id\n1\n");
    auto [valid, error] = validate_extractor_config("csv", csv_config);
    EXPECT_TRUE(valid);
    EXPECT_TRUE(error.empty());

    // Missing required parameter
    std::unordered_map<std::string, std::any> invalid_config;
    auto [invalid, error_msg] = validate_extractor_config("csv", invalid_config);
    EXPECT_FALSE(invalid);
    EXPECT_FALSE(error_msg.empty());

    // Non-existent file
    csv_config["filepath"] = std::string("/nonexistent/file.csv");
    auto [not_found, not_found_error] = validate_extractor_config("csv", csv_config);
    EXPECT_FALSE(not_found);
    EXPECT_FALSE(not_found_error.empty());

    // Valid JSON configuration
    std::unordered_map<std::string, std::any> json_config;
    json_config["filepath"] = createTestFile("valid.json", "{\"data\": [{\"id\": 1}]}");
    auto [json_valid, json_error] = validate_extractor_config("json", json_config);
    EXPECT_TRUE(json_valid);
    EXPECT_TRUE(json_error.empty());
}

// Tests create connection from URL
TEST_F(ExtractUtilsTest, CreateConnectionFromUrl) {
    // Initialize database connection factories
    PostgreSQLRegistrar::register_components();
#ifdef OMOP_HAS_MYSQL
    MySQLRegistrar::register_components();
#endif
    
    // Test PostgreSQL URL - expect connection failure since no server is running, 
    // but the database type should be recognized correctly
    try {
        auto pg_conn = omop::extract::utils::create_connection_from_url("postgresql://user:pass@localhost:5432/dbname");
        // If connection succeeds (unlikely in test environment), verify the type
        EXPECT_NE(nullptr, pg_conn);
        EXPECT_EQ("postgresql", pg_conn->get_database_type());
    } catch (const omop::common::DatabaseException& e) {
        // Connection failure is expected in test environment
        // Check that it was trying to connect to PostgreSQL
        std::string error_msg = e.what();
        EXPECT_TRUE(error_msg.find("PostgreSQL") != std::string::npos || 
                   error_msg.find("postgresql") != std::string::npos);
    }

#ifdef OMOP_HAS_MYSQL
    // Test MySQL URL - similar approach
    try {
        auto mysql_conn = omop::extract::utils::create_connection_from_url("mysql://user:pass@localhost:3306/dbname");
        EXPECT_NE(nullptr, mysql_conn);
        EXPECT_EQ("mysql", mysql_conn->get_database_type());
    } catch (const omop::common::DatabaseException& e) {
        // Connection failure is expected in test environment
        std::string error_msg = e.what();
        EXPECT_TRUE(error_msg.find("MySQL") != std::string::npos || 
                   error_msg.find("mysql") != std::string::npos);
    }
#endif

    // Test invalid URL - this should throw a configuration exception, not database exception
    EXPECT_THROW(omop::extract::utils::create_connection_from_url("invalid://url"), 
                 omop::common::ConfigurationException);
}

// Tests create extractor auto
TEST_F(ExtractUtilsTest, CreateExtractorAuto) {
    // Initialize extractors if not already done
    initialize_extractors();

    // Test CSV file detection
    std::string csv_file = createTestFile("data.csv", "id,name\n1,test\n");
    auto csv_extractor = create_extractor_auto(csv_file);
    ASSERT_NE(nullptr, csv_extractor);
    EXPECT_EQ("csv", csv_extractor->get_type());

    // Test JSON file detection
    std::string json_file = createTestFile("data.json", "[{\"id\":1}]");
    auto json_extractor = create_extractor_auto(json_file);
    ASSERT_NE(nullptr, json_extractor);
    EXPECT_EQ("json", json_extractor->get_type());

    // Test JSONL file detection
    std::string jsonl_file = createTestFile("data.jsonl", "{\"id\":1}\n{\"id\":2}\n");
    auto jsonl_extractor = create_extractor_auto(jsonl_file);
    ASSERT_NE(nullptr, jsonl_extractor);
    EXPECT_EQ("jsonl", jsonl_extractor->get_type());

    // Test directory detection
    auto dir_extractor = create_extractor_auto(test_dir_.string());
    ASSERT_NE(nullptr, dir_extractor);
    EXPECT_EQ("csv_directory", dir_extractor->get_type());
}

// Tests database config builder
TEST_F(ExtractUtilsTest, DatabaseConfigBuilder) {
    // Create database configuration directly
    std::unordered_map<std::string, std::any> config;
    config["host"] = std::string("localhost");
    config["port"] = 5432;
    config["database"] = std::string("test_db");
    config["username"] = std::string("test_user");
    config["password"] = std::string("test_pass");
    config["table"] = std::string("test_table");

    EXPECT_EQ("localhost", std::any_cast<std::string>(config["host"]));
    EXPECT_EQ(5432, std::any_cast<int>(config["port"]));
    EXPECT_EQ("test_db", std::any_cast<std::string>(config["database"]));
    EXPECT_EQ("test_user", std::any_cast<std::string>(config["username"]));
    EXPECT_EQ("test_pass", std::any_cast<std::string>(config["password"]));
    EXPECT_EQ("test_table", std::any_cast<std::string>(config["table"]));
}

// Tests detect source type
TEST_F(ExtractUtilsTest, DetectSourceType) {
    // File extensions
    EXPECT_EQ("csv", detect_source_type("data.csv"));
    EXPECT_EQ("json", detect_source_type("data.json"));
    EXPECT_EQ("jsonl", detect_source_type("data.jsonl"));
    EXPECT_EQ("jsonl", detect_source_type("data.ndjson"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.gz"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.zip"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.gz"));

    // Database URLs
    EXPECT_EQ("postgresql", detect_source_type("postgresql://localhost/db"));
    EXPECT_EQ("postgres", detect_source_type("postgres://localhost/db"));
    EXPECT_EQ("mysql", detect_source_type("mysql://localhost/db"));
    EXPECT_EQ("mariadb", detect_source_type("mariadb://localhost/db"));

    // Directory
    std::filesystem::create_directories(test_dir_ / "csv_dir");
    createTestFile("csv_dir/file1.csv", "id\n1\n");
    EXPECT_EQ("csv_directory", detect_source_type((test_dir_ / "csv_dir").string()));

    // Unknown
    EXPECT_EQ("", detect_source_type("unknown.xyz"));
}

// Tests extractor config builder
TEST_F(ExtractUtilsTest, ExtractorConfigBuilder) {
    auto config = ExtractorConfigBuilder("csv")
        .with_file("test.csv")
        .set("batch_size", size_t(1000))
        .set("max_records", size_t(5000))
        .set("continue_on_error", true)
        .get_config();

    EXPECT_EQ("csv", std::any_cast<std::string>(config["type"]));
    EXPECT_EQ("test.csv", std::any_cast<std::string>(config["filepath"]));
    EXPECT_EQ(1000, std::any_cast<size_t>(config["batch_size"]));
    EXPECT_EQ(5000, std::any_cast<size_t>(config["max_records"]));
    EXPECT_EQ(true, std::any_cast<bool>(config["continue_on_error"]));
}

// Tests extractor factory concurrent modification
TEST_F(ExtractUtilsTest, FactoryConcurrentModification) {
    // Test concurrent registration and creation
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> failure_count{0};

    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([i, &success_count, &failure_count]() {
            try {
                // Register a new type
                std::string type_name = "concurrent_type_" + std::to_string(i);
                ExtractorFactoryRegistry::register_type(type_name, []() {
                    return std::make_unique<MockIExtractor>();
                });

                // Try to create it
                auto extractor = ExtractorFactoryRegistry::create(type_name);
                if (extractor) {
                    success_count++;
                }
            } catch (const std::exception&) {
                failure_count++;
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    // Should have some successful registrations
    EXPECT_GT(success_count, 0);
}

// Tests extractor factory registry
TEST_F(ExtractUtilsTest, ExtractorFactoryRegistry) {
    // Test registration
    ExtractorFactoryRegistry::register_type("test_type", []() {
        return std::make_unique<MockIExtractor>();
    });

    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_type"));

    // Test creation
    auto extractor = ExtractorFactoryRegistry::create("test_type");
    EXPECT_NE(nullptr, extractor);

    // Test unknown type
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"), omop::common::ConfigurationException);

    // Test duplicate registration - should not throw, just overwrite
    EXPECT_NO_THROW(ExtractorFactoryRegistry::register_type("test_type", []() {
        return std::make_unique<MockIExtractor>();
    }));
}

// Tests extractor factory thread safety
TEST_F(ExtractUtilsTest, FactoryThreadSafety) {
    // Test thread-safe registration and creation
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([i, &success_count]() {
            try {
                std::string type_name = "thread_type_" + std::to_string(i);
                
                // Register type
                ExtractorFactoryRegistry::register_type(type_name, [type_name]() {
                    auto mock = std::make_unique<MockIExtractor>();
                    // Set up mock to return the correct type name
                    EXPECT_CALL(*mock, get_type())
                        .WillRepeatedly(Return(type_name));
                    return mock;
                });

                // Create extractor
                auto extractor = ExtractorFactoryRegistry::create(type_name);
                if (extractor && extractor->get_type() == type_name) {
                    success_count++;
                }
            } catch (const std::exception&) {
                // Expected for duplicate registrations
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    // Should have successful operations
    EXPECT_GT(success_count, 0);
}

// Tests get extractor info
TEST_F(ExtractUtilsTest, GetExtractorInfo) {
    auto info_list = get_extractor_info();
    EXPECT_FALSE(info_list.empty());

    // Check that each info entry has required fields
    for (const auto& info : info_list) {
        EXPECT_FALSE(info.type.empty());
        EXPECT_FALSE(info.description.empty());
    }

    // Check for specific expected types
    auto csv_info = std::find_if(info_list.begin(), info_list.end(),
        [](const auto& info) { return info.type == "csv"; });
    EXPECT_NE(csv_info, info_list.end());
}

// Tests initialize extractors
TEST_F(ExtractUtilsTest, InitializeExtractors) {
    // Clear any existing registrations
    ExtractorFactoryRegistry::clear();

    // Initialize extractors
    EXPECT_NO_THROW(initialize_extractors());

    // Verify some expected types are registered
    std::vector<std::string> expected_types = {
        "csv", "json", "jsonl", "mysql", "postgresql"
    };

    for (const auto& type : expected_types) {
        if (ExtractorFactoryRegistry::is_type_registered(type)) {
            auto extractor = ExtractorFactoryRegistry::create(type);
            EXPECT_NE(nullptr, extractor);
            EXPECT_EQ(type, extractor->get_type());
        }
    }

    // Test that calling initialize again is safe
    EXPECT_NO_THROW(initialize_extractors());
}

// Tests ParallelExtractor
TEST_F(ExtractUtilsTest, DISABLED_ParallelExtractor) {
    // DISABLED: Mock objects are not thread-safe and cause crashes
    // Alternative implementation using real extractors is available in ParallelExtractorWithRealExtractors
}

// New thread-safe test using real CSV extractors
TEST_F(ExtractUtilsTest, ParallelExtractorWithRealExtractors) {
    ParallelExtractor::Config config;
    config.num_threads = 2;
    config.queue_size = 5;

    ParallelExtractor parallel_extractor(config);

    // Add multiple extractors
    for (int i = 0; i < 3; ++i) {
        // Create test CSV file
        std::string csv_content = std::format("extractor_id,value\n{},{}\n", i, i * 100);
        std::string filepath = createTestFile(std::format("parallel_{}.csv", i), csv_content);
        
        // Create real CSV extractor
        auto csv_extractor = std::make_unique<CsvExtractor>();
        std::unordered_map<std::string, std::any> csv_config;
        csv_config["filepath"] = filepath;
        
        ProcessingContext init_context;
        csv_extractor->initialize(csv_config, init_context);
        
        parallel_extractor.add_extractor(std::move(csv_extractor),
                                        std::format("extractor_{}", i));
    }

    // Extract all records
    auto all_records = parallel_extractor.extract_all();
    EXPECT_EQ(3, all_records.size()); // 3 extractors * 1 record each
}

// Tests ParallelExtractor streaming
TEST_F(ExtractUtilsTest, DISABLED_ParallelExtractorStreaming) {
    // DISABLED: Mock objects are not thread-safe
}

// New thread-safe streaming test
TEST_F(ExtractUtilsTest, ParallelExtractorStreamingWithRealExtractors) {
    ParallelExtractor parallel_extractor;

    // Create test CSV file
    std::string csv_content = "id\n1\n";
    std::string filepath = createTestFile("streaming_test.csv", csv_content);
    
    // Create real CSV extractor
    auto csv_extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = filepath;
    
    ProcessingContext init_context;
    csv_extractor->initialize(csv_config, init_context);
    
    parallel_extractor.add_extractor(std::move(csv_extractor), "test");

    std::atomic<int> batch_count{0};
    std::mutex result_mutex;
    parallel_extractor.extract_streaming(
        [&batch_count, &result_mutex](const RecordBatch& b, const std::string& name) {
            std::lock_guard<std::mutex> lock(result_mutex);
            batch_count++;
            EXPECT_EQ("test", name);
            EXPECT_EQ(1, b.size());
        });

    EXPECT_EQ(1, batch_count);
}

// Tests progress tracking
TEST_F(ExtractUtilsTest, ProgressTracking) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _)).WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;
    BatchExtractor::Config config;
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.emplace_back(current, total);
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(5, records.size());
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(5, progress_updates.back().first);
    EXPECT_EQ(5, progress_updates.back().second);
}

// Tests utility extract CSV
TEST_F(ExtractUtilsTest, UtilityExtractCsv) {
    // Create test CSV file
    std::string csv_content = "id,name,value\n1,John,100.5\n2,Jane,200.75\n";
    std::string csv_file = createTestFile("test.csv", csv_content);

    // Test CSV extraction utility
    auto records = omop::extract::utils::extract_csv(csv_file);
    EXPECT_EQ(2, records.size());

    // Safely check field types and values
    EXPECT_TRUE(records[0].hasField("id"));
    EXPECT_TRUE(records[0].hasField("name"));

    // Get fields and check their types
    auto id_field = records[0].getField("id");
    auto name_field = records[0].getField("name");

    // Handle multiple possible types for id field
    if (id_field.type() == typeid(std::string)) {
        EXPECT_EQ("1", std::any_cast<std::string>(id_field));
    } else if (id_field.type() == typeid(int)) {
        EXPECT_EQ(1, std::any_cast<int>(id_field));
    } else if (id_field.type() == typeid(long long)) {
        EXPECT_EQ(1LL, std::any_cast<long long>(id_field));
    } else {
        FAIL() << "Unexpected type for id field: " << id_field.type().name();
    }

    // Name field should be string
    if (name_field.type() == typeid(std::string)) {
        EXPECT_EQ("John", std::any_cast<std::string>(name_field));
    } else {
        FAIL() << "Unexpected type for name field: " << name_field.type().name();
    }
}

// Tests utility extract JSON
TEST_F(ExtractUtilsTest, UtilityExtractJson) {
    // Create test JSON file
    std::string json_content = R"([
        {"id": 1, "name": "John", "active": true},
        {"id": 2, "name": "Jane", "active": false}
    ])";
    std::string json_file = createTestFile("test.json", json_content);

    // Test JSON extraction utility
    auto records = omop::extract::utils::extract_json(json_file);
    EXPECT_EQ(2, records.size());

    // Verify first record fields exist
    EXPECT_TRUE(records[0].hasField("id"));
    EXPECT_TRUE(records[0].hasField("name"));
    EXPECT_TRUE(records[0].hasField("active"));

    // Get fields and check their types safely
    auto id_field = records[0].getField("id");
    auto name_field = records[0].getField("name");
    auto active_field = records[0].getField("active");

    // Handle multiple possible types for id field
    if (id_field.type() == typeid(int)) {
        EXPECT_EQ(1, std::any_cast<int>(id_field));
    } else if (id_field.type() == typeid(long)) {
        EXPECT_EQ(1L, std::any_cast<long>(id_field));
    } else if (id_field.type() == typeid(long long)) {
        EXPECT_EQ(1LL, std::any_cast<long long>(id_field));
    } else if (id_field.type() == typeid(double)) {
        EXPECT_EQ(1.0, std::any_cast<double>(id_field));
    } else if (id_field.type() == typeid(std::string)) {
        EXPECT_EQ("1", std::any_cast<std::string>(id_field));
    } else {
        FAIL() << "Unexpected type for id field: " << id_field.type().name();
    }

    // Name field should be string
    if (name_field.type() == typeid(std::string)) {
        EXPECT_EQ("John", std::any_cast<std::string>(name_field));
    } else {
        FAIL() << "Unexpected type for name field: " << name_field.type().name();
    }

    // Active field should be bool
    if (active_field.type() == typeid(bool)) {
        EXPECT_EQ(true, std::any_cast<bool>(active_field));
    } else if (active_field.type() == typeid(std::string)) {
        EXPECT_EQ("true", std::any_cast<std::string>(active_field));
    } else {
        FAIL() << "Unexpected type for active field: " << active_field.type().name();
    }
}

File tests/unit/extract/extract_utils_extended_test.cpp:

/**
 * @file extract_utils_extended_test.cpp
 * @brief Extended unit tests for extract utilities covering missing test cases
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extract.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <atomic>
#include <mutex>

namespace omop::extract::test {

using namespace ::testing;
using namespace omop::core;
using namespace omop::common;

// Mock database connection for testing extract_table
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD((std::unique_ptr<IPreparedStatement>), prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, ((const std::string& table_name), (const std::string& schema)), (const, override));
};

// Mock result set for database tests
class MockResultSet : public IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t index), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string& column_name), (const, override));
    MOCK_METHOD(bool, is_null, (size_t index), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string& column_name), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t index), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t index), (const, override));
    MOCK_METHOD(Record, to_record, (), (const, override));
};

// Mock extractor with statistics
class MockExtractorWithStats : public IExtractor {
public:
    MOCK_METHOD(void, initialize,
                ((const std::unordered_map<std::string, std::any>&), (ProcessingContext&)),
                (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
};

// Test fixture for extended tests
class ExtractUtilsExtendedTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_extract_extended_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests BatchExtractor statistics collection
TEST_F(ExtractUtilsExtendedTest, BatchExtractorStatistics) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    // Set up mock statistics
    std::unordered_map<std::string, std::any> mock_stats;
    mock_stats["total_records"] = size_t(100);
    mock_stats["extraction_time_seconds"] = 5.5;
    mock_stats["errors"] = size_t(2);

    EXPECT_CALL(*mock_ptr, get_statistics())
        .WillRepeatedly(Return(mock_stats));

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch));

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)));
    batch_extractor.extract_all();

    // Get and verify statistics
    auto stats = batch_extractor.get_statistics();

    // Should include original extractor stats
    EXPECT_TRUE(stats.contains("total_records"));
    EXPECT_EQ(size_t(100), std::any_cast<size_t>(stats["total_records"]));

    // Should include batch extractor specific stats
    EXPECT_TRUE(stats.contains("batch_size"));
    EXPECT_TRUE(stats.contains("max_records"));
    EXPECT_TRUE(stats.contains("continue_on_error"));
}

// Tests ParallelExtractor statistics aggregation
// DISABLED: ParallelExtractor tests cause crashes due to threading issues with mocks
TEST_F(ExtractUtilsExtendedTest, DISABLED_ParallelExtractorStatistics) {
    auto parallel_extractor = std::make_unique<ParallelExtractor>();

    // Add multiple extractors with different statistics
    for (int i = 0; i < 3; ++i) {
        auto mock = std::make_unique<MockExtractorWithStats>();

        std::unordered_map<std::string, std::any> stats;
        stats["records_extracted"] = size_t(100 * (i + 1));
        stats["errors"] = size_t(i);
        stats["extractor_id"] = i;

        EXPECT_CALL(*mock, get_statistics())
            .WillRepeatedly(Return(stats));

        EXPECT_CALL(*mock, has_more_data())
            .WillOnce(Return(false));

        parallel_extractor->add_extractor(std::move(mock),
                                        std::format("extractor_{}", i));
    }

    // Extract data (minimal to focus on statistics)
    parallel_extractor->extract_all();

    // Get aggregated statistics
    auto all_stats = parallel_extractor->get_all_statistics();

    EXPECT_EQ(3, all_stats.size());

    // Verify each extractor's statistics
    for (int i = 0; i < 3; ++i) {
        std::string name = std::format("extractor_{}", i);
        EXPECT_TRUE(all_stats.contains(name));

        auto& stats = all_stats[name];
        EXPECT_TRUE(stats.contains("records_extracted"));
        EXPECT_EQ(size_t(100 * (i + 1)),
                  std::any_cast<size_t>(stats["records_extracted"]));
    }
}

// Tests utils::extract_table function
TEST_F(ExtractUtilsExtendedTest, ExtractTable) {
    auto mock_connection = std::make_unique<MockDatabaseConnection>();
    auto conn_ptr = mock_connection.get();

    // Set up connection expectations
    EXPECT_CALL(*conn_ptr, is_connected())
        .WillRepeatedly(Return(true));

    EXPECT_CALL(*conn_ptr, table_exists("person", ""))
        .WillOnce(Return(true));

    EXPECT_CALL(*conn_ptr, get_database_type())
        .WillRepeatedly(Return("postgresql"));

    // Create mock result set
    auto mock_result = std::make_unique<MockResultSet>();
    auto result_ptr = mock_result.get();

    // Set up result set to return 3 records
    EXPECT_CALL(*result_ptr, next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    Record r1, r2, r3;
    r1.setField("person_id", 1);
    r1.setField("name", std::string("John"));
    r2.setField("person_id", 2);
    r2.setField("name", std::string("Jane"));
    r3.setField("person_id", 3);
    r3.setField("name", std::string("Bob"));

    EXPECT_CALL(*result_ptr, to_record())
        .WillOnce(Return(r1))
        .WillOnce(Return(r2))
        .WillOnce(Return(r3));

    EXPECT_CALL(*conn_ptr, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    // Test extract_table
    auto records = utils::extract_table(std::move(mock_connection),
                                       "person",
                                       "person_id > 0");

    EXPECT_EQ(3, records.size());
    EXPECT_EQ(1, records[0].getFieldAs<int>("person_id"));
    EXPECT_EQ("Jane", records[1].getFieldAs<std::string>("name"));
}

// Tests case-insensitive file extension detection
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeCaseInsensitive) {
    // Test uppercase extensions
    EXPECT_EQ("csv", detect_source_type("DATA.CSV"));
    EXPECT_EQ("json", detect_source_type("DATA.JSON"));
    EXPECT_EQ("jsonl", detect_source_type("DATA.JSONL"));

    // Test mixed case
    EXPECT_EQ("compressed_csv", detect_source_type("data.CsV.Gz"));
    EXPECT_EQ("compressed_csv", detect_source_type("DATA.csv.ZIP"));

    // Test complex patterns
    EXPECT_EQ("compressed_csv", detect_source_type("my.data.csv.GZ"));
    EXPECT_EQ("compressed_csv", detect_source_type("report_2024.CSV.BZ2"));
}

// Tests directory detection with no CSV files
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeEmptyDirectory) {
    // Create directory with no CSV files
    std::filesystem::create_directories(test_dir_ / "empty_dir");
    createTestFile("empty_dir/data.txt", "text file");
    createTestFile("empty_dir/data.json", "{}");

    // Should return empty string for directory without CSV files
    EXPECT_EQ("", detect_source_type((test_dir_ / "empty_dir").string()));
}

// Tests extended configuration validation
TEST_F(ExtractUtilsExtendedTest, ValidateExtractorConfigExtended) {
    // Test JSON configuration validation
    std::unordered_map<std::string, std::any> json_config;
    json_config["filepath"] = std::string(createTestFile("test.json", "[]"));

    auto [valid_json, error_json] = validate_extractor_config("json", json_config);
    EXPECT_TRUE(valid_json);
    EXPECT_TRUE(error_json.empty());

    // Test CSV directory configuration
    std::filesystem::create_directories(test_dir_ / "csv_test");
    createTestFile("csv_test/file.csv", "id\n1\n");

    std::unordered_map<std::string, std::any> dir_config;
    dir_config["directory"] = std::string((test_dir_ / "csv_test").string());

    auto [valid_dir, error_dir] = validate_extractor_config("csv_directory", dir_config);
    EXPECT_TRUE(valid_dir);

    // Test non-existent directory
    dir_config["directory"] = std::string("/nonexistent/directory");
    auto [invalid_dir, error_msg] = validate_extractor_config("csv_directory", dir_config);
    EXPECT_FALSE(invalid_dir);
    EXPECT_NE(error_msg.find("not found"), std::string::npos);
}

// Tests URL parsing edge cases
TEST_F(ExtractUtilsExtendedTest, CreateConnectionFromUrlEdgeCases) {
    // Test URL without port (should use default)
    EXPECT_NO_THROW({
        try {
            utils::create_connection_from_url("postgresql://user:pass@localhost/db");
        } catch (const std::exception& e) {
            // Expected in test environment without actual database
            std::string error = e.what();
            // Should parse URL successfully even if connection fails
            EXPECT_TRUE(error.find("Invalid database URL") == std::string::npos);
        }
    });

    // Test URL without credentials
    EXPECT_NO_THROW({
        try {
            utils::create_connection_from_url("mysql://localhost:3306/testdb");
        } catch (const std::exception& e) {
            std::string error = e.what();
            EXPECT_TRUE(error.find("Invalid database URL") == std::string::npos);
        }
    });

    // Test various database types
    std::vector<std::string> db_types = {"postgresql", "postgres", "mysql", "mariadb"};
    for (const auto& db_type : db_types) {
        std::string url = db_type + "://localhost/test";
        EXPECT_NO_THROW({
            try {
                utils::create_connection_from_url(url);
            } catch (const ConfigurationException&) {
                // Re-throw configuration exceptions (invalid URL format)
                throw;
            } catch (...) {
                // Other exceptions (connection failures) are expected
            }
        });
    }
}

// Tests BatchExtractor with empty batches
TEST_F(ExtractUtilsExtendedTest, BatchExtractorEmptyBatches) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    // Set up to return normal batch first, then empty batch to stop
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))  // First call - will get normal batch
        .WillOnce(Return(true)); // Second call - will get empty batch and stop

    RecordBatch empty_batch;
    RecordBatch normal_batch;
    normal_batch.addRecord(Record());

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(normal_batch))  // Return normal batch first
        .WillOnce(Return(empty_batch));  // Then empty batch to stop

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)));
    auto records = batch_extractor.extract_all();

    // Should handle empty batches gracefully
    EXPECT_EQ(1, records.size());
}

// Tests ParallelExtractor with failed extractors
// DISABLED: ParallelExtractor tests cause crashes due to threading issues with mocks
TEST_F(ExtractUtilsExtendedTest, DISABLED_ParallelExtractorWithFailures) {
    auto parallel_extractor = std::make_unique<ParallelExtractor>();

    // Add extractor that will fail
    auto failing_mock = std::make_unique<MockExtractorWithStats>();
    EXPECT_CALL(*failing_mock, has_more_data())
        .WillOnce(Return(true));

    ProcessingContext context;
    EXPECT_CALL(*failing_mock, extract_batch(_, _))
        .WillOnce(Throw(std::runtime_error("Extraction failed")));

    // Add normal extractor
    auto normal_mock = std::make_unique<MockExtractorWithStats>();
    EXPECT_CALL(*normal_mock, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    batch.addRecord(Record());
    EXPECT_CALL(*normal_mock, extract_batch(_, _))
        .WillOnce(Return(batch));

    parallel_extractor->add_extractor(std::move(failing_mock), "failing");
    parallel_extractor->add_extractor(std::move(normal_mock), "normal");

    // Should continue despite one extractor failing
    auto records = parallel_extractor->extract_all();
    EXPECT_EQ(1, records.size());
}

// Tests progress callback with zero total
TEST_F(ExtractUtilsExtendedTest, ProgressCallbackZeroTotal) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 5; ++i) {
        batch.addRecord(Record());
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;

    BatchExtractor::Config config;
    config.batch_size = 10;
    config.max_records = 0; // No limit
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.push_back({current, total});
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);
    batch_extractor.extract_all();

    // When max_records is 0, total should be 0 in progress updates
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(0, progress_updates[0].second);
}

// Tests detect_source_type with symbolic links
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeSymbolicLinks) {
    // Create a CSV file and a symbolic link to it
    std::string csv_file = createTestFile("original.csv", "id\n1\n");
    std::filesystem::path symlink = test_dir_ / "link_to_csv.csv";

    // Create symbolic link (if supported by the filesystem)
    try {
        std::filesystem::create_symlink(csv_file, symlink);
        EXPECT_EQ("csv", detect_source_type(symlink.string()));
    } catch (const std::filesystem::filesystem_error&) {
        // Skip test if symbolic links are not supported
        GTEST_SKIP() << "Symbolic links not supported on this filesystem";
    }
}

// Tests configuration validation for unknown extractor type
TEST_F(ExtractUtilsExtendedTest, ValidateUnknownExtractorType) {
    std::unordered_map<std::string, std::any> config;
    config["some_param"] = "value";

    auto [valid, error] = validate_extractor_config("unknown_type", config);
    EXPECT_FALSE(valid);
    EXPECT_NE(error.find("Unknown extractor type"), std::string::npos);
}

// Thread-safe mock extractor for parallel testing
class ThreadSafeMockExtractor : public core::IExtractor {
private:
    mutable std::mutex mutex_;
    std::atomic<bool> has_data_{true};
    std::atomic<size_t> extracted_count_{0};
    size_t total_records_;
    std::string type_;

public:
    explicit ThreadSafeMockExtractor(const std::string& type, size_t records = 10)
        : type_(type), total_records_(records) {}

    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override {
        // Thread-safe initialization
    }

    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override {
        std::lock_guard<std::mutex> lock(mutex_);
        core::RecordBatch batch;
        
        size_t current = extracted_count_.load();
        size_t to_extract = std::min(batch_size, total_records_ - current);
        
        for (size_t i = 0; i < to_extract; ++i) {
            core::Record r;
            r.setField("id", static_cast<int>(current + i));
            r.setField("value", static_cast<int>((current + i) * 100));
            batch.addRecord(r);
        }
        
        extracted_count_ += to_extract;
        if (extracted_count_ >= total_records_) {
            has_data_ = false;
        }
        
        return batch;
    }

    bool has_more_data() const override { return has_data_.load(); }
    std::string get_type() const override { return type_; }
    void finalize(core::ProcessingContext& context) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"extracted", extracted_count_.load()}};
    }
};

// Replace the disabled ParallelExtractor test with thread-safe implementation
TEST_F(ExtractUtilsExtendedTest, ParallelExtractorThreadSafe) {
    ParallelExtractor::Config config;
    config.num_threads = 4;
    config.preserve_order = false;

    ParallelExtractor parallel_extractor(config);

    // Add thread-safe extractors
    for (int i = 0; i < 6; ++i) {
        parallel_extractor.add_extractor(
            std::make_unique<ThreadSafeMockExtractor>(
                std::format("extractor_{}", i), 100),
            std::format("extractor_{}", i));
    }

    auto records = parallel_extractor.extract_all();
    EXPECT_EQ(600, records.size());

    auto all_stats = parallel_extractor.get_all_statistics();
    for (const auto& [name, stats] : all_stats) {
        EXPECT_EQ(100, std::any_cast<size_t>(stats.at("extracted")));
    }
}

} // namespace omop::extract::test


File tests/unit/extract/extractor_factory_test.cpp:

/**
 * @file extractor_factory_impl_test.cpp
 * @brief Unit tests for ExtractorFactoryRegistry and related factory functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_factory.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <thread>
#include <set>
#include <iostream>

namespace omop::extract::test {

using namespace ::testing;

// Mock ProcessingContext for testing
// Note: ProcessingContext methods are not virtual, so we use composition instead of inheritance
class MockProcessingContext {
public:
    // Mock methods that would be useful for testing
    MOCK_METHOD(void, log, (const std::string& level, const std::string& message));
    MOCK_METHOD(void, increment_errors, ());
    MOCK_METHOD(bool, should_continue_on_error, (), (const));

    // Provide a real ProcessingContext for tests that need it
    core::ProcessingContext& get_real_context() { return real_context_; }

private:
    core::ProcessingContext real_context_;
};

// Mock extractor for testing
class MockExtractor : public core::IExtractor {
public:
    explicit MockExtractor(const std::string& type = "mock") : type_(type) {}

    MOCK_METHOD(void, initialize,
        ((const std::unordered_map<std::string, std::any>&), (core::ProcessingContext&)),
        (override));
    MOCK_METHOD(core::RecordBatch, extract_batch,
        (size_t batch_size, core::ProcessingContext& context),
        (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(void, finalize, (core::ProcessingContext& context), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));

    std::string get_type() const override { return type_; }

private:
    std::string type_;
};

// Test fixture for factory tests
class ExtractorFactoryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Don't clear registrations - let each test manage its own state
    }

    void TearDown() override {
        // Don't clear registrations - let each test manage its own state
    }
};

// ============================================================================
// ExtractorFactoryTest Tests
// ============================================================================

// Tests clear registry functionality
TEST_F(ExtractorFactoryTest, ClearRegistry) {
    // Register a type
    ExtractorFactoryRegistry::register_type("test_clear",
        []() { return std::make_unique<MockExtractor>("test_clear"); });

    // Verify it's registered
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_clear"));

    // Clear registry
    ExtractorFactoryRegistry::clear();

    // Verify it's no longer registered
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("test_clear"));
    EXPECT_THROW(ExtractorFactoryRegistry::create("test_clear"),
                 common::ConfigurationException);
}

// Tests create extractor with config
TEST_F(ExtractorFactoryTest, CreateExtractorWithConfig) {
    // Register a type
    ExtractorFactoryRegistry::register_type("config_test",
        []() { return std::make_unique<MockExtractor>("config_test"); });

    // Create with config
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(1000);
    config["max_records"] = size_t(5000);

    auto extractor = ExtractorFactoryRegistry::create("config_test");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "config_test");
}

// Tests create unknown type
TEST_F(ExtractorFactoryTest, CreateUnknownType) {
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("unknown_type"));
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"),
                 common::ConfigurationException);
}

// Tests create various extractor types
TEST_F(ExtractorFactoryTest, CreateVariousExtractorTypes) {
    // Initialize built-in extractors
    initialize_extractors();

    struct TestCase {
        std::string type;
        std::string expected_class_type;
    };

    std::vector<TestCase> test_cases = {
        {"csv", "CsvExtractor"},
        {"json", "JsonExtractor"},
        {"jsonl", "JsonExtractor"},
        {"json_lines", "JsonExtractor"},
        {"streaming_json", "JsonExtractor"},
        {"multi_csv", "CsvExtractor"},
        {"csv_directory", "CsvExtractor"},
        {"compressed_csv", "CompressedCsvExtractor"}
    };

    for (const auto& test_case : test_cases) {
        if (ExtractorFactoryRegistry::is_type_registered(test_case.type)) {
            auto extractor = ExtractorFactoryRegistry::create(test_case.type);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), test_case.type);
        }
    }
}

// Tests database extractor aliases
TEST_F(ExtractorFactoryTest, DatabaseExtractorAliases) {
    // Initialize built-in extractors
    initialize_extractors();

    // Test database extractor aliases
    std::vector<std::string> db_aliases = {
        "mysql", "postgresql", "postgres", "sqlite", "oracle", "sqlserver"
    };

    for (const auto& alias : db_aliases) {
        if (ExtractorFactoryRegistry::is_type_registered(alias)) {
            auto extractor = ExtractorFactoryRegistry::create(alias);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), alias);
        }
    }
}

// Tests empty type handling
TEST_F(ExtractorFactoryTest, EmptyTypeHandling) {
    // Test empty type creation
    EXPECT_THROW(ExtractorFactoryRegistry::create(""),
                 common::ConfigurationException);
}

// Tests exception details
TEST_F(ExtractorFactoryTest, ExceptionDetails) {
    try {
        ExtractorFactoryRegistry::create("nonexistent_type");
        FAIL() << "Expected ConfigurationException";
    } catch (const common::ConfigurationException& e) {
        std::string message = e.what();
        EXPECT_TRUE(message.find("nonexistent_type") != std::string::npos);
        // Different implementations may have different error messages
    }
}

// Tests extractor config builder
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilder) {
    // Test config builder for file-based extractors
    auto config = ExtractorConfigBuilder("csv")
        .with_file("test.csv")
        .set("batch_size", size_t(1000))
        .set("max_records", size_t(5000))
        .get_config();

    EXPECT_EQ(std::any_cast<std::string>(config["type"]), std::string("csv"));
    EXPECT_EQ(std::any_cast<std::string>(config["filepath"]), std::string("test.csv"));
    EXPECT_EQ(std::any_cast<size_t>(config["batch_size"]), size_t(1000));
    EXPECT_EQ(std::any_cast<size_t>(config["max_records"]), size_t(5000));
}

// Tests extractor config builder database
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilderDatabase) {
    // Test config builder for database extractors
    auto config = ExtractorConfigBuilder("mysql")
        .with_database("localhost", 3306, "test_db", "user", "pass")
        .with_table("table")
        .get_config();

    EXPECT_EQ(std::any_cast<std::string>(config["type"]), std::string("mysql"));
    EXPECT_EQ(std::any_cast<std::string>(config["host"]), std::string("localhost"));
    EXPECT_EQ(std::any_cast<int>(config["port"]), int(3306));
    EXPECT_EQ(std::any_cast<std::string>(config["database"]), std::string("test_db"));
    EXPECT_EQ(std::any_cast<std::string>(config["username"]), std::string("user"));
    EXPECT_EQ(std::any_cast<std::string>(config["password"]), std::string("pass"));
    EXPECT_EQ(std::any_cast<std::string>(config["table"]), std::string("table"));
}

// Tests get extractor info
TEST_F(ExtractorFactoryTest, GetExtractorInfo) {
    // Get info for all extractors
    auto info_list = get_extractor_info();
    EXPECT_FALSE(info_list.empty());

    // Check that each info entry has required fields
    for (const auto& info : info_list) {
        EXPECT_FALSE(info.type.empty());
        EXPECT_FALSE(info.description.empty());
    }

    // Check for specific expected types
    auto csv_info = std::find_if(info_list.begin(), info_list.end(),
        [](const auto& info) { return info.type == "csv"; });
    EXPECT_NE(csv_info, info_list.end());
}

// Tests get registered types
TEST_F(ExtractorFactoryTest, GetRegisteredTypes) {
    // Clear any existing registrations
    ExtractorFactoryRegistry::clear();

    // Register multiple types
    std::set<std::string> expected_types = {"type_a", "type_b", "type_c"};

    for (const auto& type : expected_types) {
        ExtractorFactoryRegistry::register_type(type,
            [type]() { return std::make_unique<MockExtractor>(type); });
    }

    // Get registered types
    auto registered = ExtractorFactoryRegistry::get_registered_types();

    // Should be sorted
    EXPECT_TRUE(std::is_sorted(registered.begin(), registered.end()));

    // Should contain all expected types
    std::set<std::string> actual_types(registered.begin(), registered.end());
    EXPECT_EQ(actual_types, expected_types);
}

// Tests initialize builtin extractors
TEST_F(ExtractorFactoryTest, InitializeBuiltinExtractors) {
    // Initialize built-in extractors
    initialize_extractors();

    // Print all registered types for debugging
    auto all_types = ExtractorFactoryRegistry::get_registered_types();
    std::cout << "All registered extractor types:" << std::endl;
    for (const auto& type : all_types) {
        std::cout << "  - " << type << std::endl;
    }

    // Verify some expected built-in types are registered
    std::vector<std::string> expected_types = {
        "csv", "multi_csv", "csv_directory", "compressed_csv",
        "json", "jsonl", "json_lines", "streaming_json",
        "mysql", "postgresql", "postgres", "sqlite"
    };

    for (const auto& type : expected_types) {
        if (ExtractorFactoryRegistry::is_type_registered(type)) {
            auto extractor = ExtractorFactoryRegistry::create(type);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), type);
        }
    }
}

// Tests odbc conditional support
TEST_F(ExtractorFactoryTest, OdbcConditionalSupport) {
    // Initialize built-in extractors
    initialize_extractors();

    // Test ODBC support (may not be available on all platforms)
    if (ExtractorFactoryRegistry::is_type_registered("odbc")) {
        auto extractor = ExtractorFactoryRegistry::create("odbc");
        EXPECT_NE(extractor, nullptr);
        EXPECT_EQ(extractor->get_type(), "odbc");
    }
}

// Tests register and create extractor
TEST_F(ExtractorFactoryTest, RegisterAndCreateExtractor) {
    // Register a mock extractor type
    ExtractorFactoryRegistry::register_type("test_mock",
        []() { return std::make_unique<MockExtractor>("test_mock"); });

    // Verify registration
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_mock"));

    // Create extractor
    auto extractor = ExtractorFactoryRegistry::create("test_mock");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "test_mock");
}

// Tests register duplicate type
TEST_F(ExtractorFactoryTest, RegisterDuplicateType) {
    // Register a type
    ExtractorFactoryRegistry::register_type("duplicate_test",
        []() { return std::make_unique<MockExtractor>("duplicate_test"); });

    // Try to register the same type again - may not throw, some implementations allow overriding
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("duplicate_test"));
}

// Tests thread safe creation
TEST_F(ExtractorFactoryTest, ThreadSafeCreation) {
    // Register a type
    ExtractorFactoryRegistry::register_type("concurrent_type",
        []() { return std::make_unique<MockExtractor>("concurrent_type"); });

    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    // Launch multiple threads to create extractors
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&success_count]() {
            try {
                auto extractor = ExtractorFactoryRegistry::create("concurrent_type");
                if (extractor && extractor->get_type() == "concurrent_type") {
                    success_count++;
                }
            } catch (...) {
                // Should not throw
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // All creations should succeed
    EXPECT_EQ(success_count, 20);
}

// Tests thread safe registration
TEST_F(ExtractorFactoryTest, ThreadSafeRegistration) {
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    // Launch multiple threads to register different types
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([i, &success_count]() {
            std::string type = "thread_type_" + std::to_string(i);
            try {
                ExtractorFactoryRegistry::register_type(type,
                    [type]() { return std::make_unique<MockExtractor>(type); });
                success_count++;
            } catch (...) {
                // Registration might fail if type already exists
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // All registrations should succeed
    EXPECT_EQ(success_count, 10);

    // Verify all types are registered
    for (int i = 0; i < 10; ++i) {
        std::string type = "thread_type_" + std::to_string(i);
        EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(type));
    }
}

} // namespace omop::extract::test


File tests/unit/extract/postgresql_connector_test.cpp:

/**
 * @file postgresql_connector_test.cpp
 * @brief Unit tests for PostgreSQL connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/postgresql_connector.h"
#include "common/exceptions.h"
#include <cstring>
#include <memory>
#include <atomic>

using namespace omop::extract;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::StrEq;

// Mock PGresult for testing
struct MockPGresult {
    int ntuples;
    int nfields;
    std::vector<std::string> field_names;
    std::vector<Oid> field_types;
    std::vector<std::vector<std::string>> data;
    std::vector<std::vector<bool>> is_null;
};

// Mock libpq functions
static MockPGresult* current_mock_result = nullptr;
static PGconn* mock_connection = reinterpret_cast<PGconn*>(0x12345678);
static ConnStatusType mock_connection_status = CONNECTION_OK;
static std::string mock_error_message = "";
static std::unique_ptr<MockPGresult> mock_result_storage;

// Override libpq functions for testing
extern "C" {
    PGconn* PQconnectdb(const char*) { return mock_connection; }
    void PQfinish(PGconn*) {}
    ConnStatusType PQstatus(const PGconn*) { return mock_connection_status; }
    char* PQerrorMessage(const PGconn*) { return const_cast<char*>(mock_error_message.c_str()); }
    int PQsetClientEncoding(PGconn*, const char*) { return 0; }

    PGresult* PQexec(PGconn*, const char*) {
        if (current_mock_result) {
            // Transfer ownership to avoid double delete
            mock_result_storage.reset(current_mock_result);
            current_mock_result = mock_result_storage.get();
            return reinterpret_cast<PGresult*>(current_mock_result);
        }
        return nullptr;
    }

    PGresult* PQprepare(PGconn*, const char*, const char*, int, const Oid*) {
        return reinterpret_cast<PGresult*>(current_mock_result);
    }

    PGresult* PQexecPrepared(PGconn*, const char*, int, const char* const*,
                            const int*, const int*, int) {
        return reinterpret_cast<PGresult*>(current_mock_result);
    }

    void PQclear(PGresult*) {}

    ExecStatusType PQresultStatus(const PGresult*) {
        return current_mock_result ? PGRES_TUPLES_OK : PGRES_FATAL_ERROR;
    }

    int PQntuples(const PGresult*) {
        return current_mock_result ? current_mock_result->ntuples : 0;
    }

    int PQnfields(const PGresult*) {
        return current_mock_result ? current_mock_result->nfields : 0;
    }

    char* PQfname(const PGresult*, int field_num) {
        if (current_mock_result && field_num < current_mock_result->nfields) {
            return const_cast<char*>(current_mock_result->field_names[field_num].c_str());
        }
        return nullptr;
    }

    Oid PQftype(const PGresult*, int field_num) {
        if (current_mock_result && field_num < current_mock_result->nfields) {
            return current_mock_result->field_types[field_num];
        }
        return 0;
    }

    int PQfnumber(const PGresult*, const char* field_name) {
        if (!current_mock_result) return -1;
        for (int i = 0; i < current_mock_result->nfields; ++i) {
            if (current_mock_result->field_names[i] == field_name) {
                return i;
            }
        }
        return -1;
    }

    char* PQgetvalue(const PGresult*, int tup_num, int field_num) {
        if (current_mock_result &&
            tup_num < current_mock_result->ntuples &&
            field_num < current_mock_result->nfields) {
            return const_cast<char*>(current_mock_result->data[tup_num][field_num].c_str());
        }
        return nullptr;
    }

    int PQgetisnull(const PGresult*, int tup_num, int field_num) {
        if (current_mock_result &&
            tup_num < current_mock_result->ntuples &&
            field_num < current_mock_result->nfields) {
            return current_mock_result->is_null[tup_num][field_num] ? 1 : 0;
        }
        return 1;
    }

    char* PQcmdTuples(PGresult*) {
        static char buffer[32];
        snprintf(buffer, sizeof(buffer), "%d", current_mock_result ? current_mock_result->ntuples : 0);
        return buffer;
    }
}

// Test fixture for PostgreSQL tests
class PostgreSQLTest : public ::testing::Test {
protected:
    void SetUp() override {
        current_mock_result = nullptr;
        mock_connection_status = CONNECTION_OK;
        mock_error_message = "";
        // Initialize statement counter for unique statement names
        statement_counter_ = 0;
    }

    void TearDown() override {
        // Safe cleanup of mock result
        if (current_mock_result) {
            delete current_mock_result;
        }
        current_mock_result = nullptr;
        
        // Reset all mock state
        mock_connection_status = CONNECTION_BAD;
        mock_error_message.clear();
    }

private:
    static std::atomic<int> statement_counter_;
    
public:
    MockPGresult* createMockResult(int rows, int cols) {
        // Ensure any existing result is cleaned up first
        if (current_mock_result) {
            delete current_mock_result;
            current_mock_result = nullptr;
        }
        
        auto result = new MockPGresult;
        result->ntuples = rows;
        result->nfields = cols;
        result->data.resize(rows);
        result->is_null.resize(rows);
        for (int i = 0; i < rows; ++i) {
            result->data[i].resize(cols);
            result->is_null[i].resize(cols, false);
        }
        current_mock_result = result;
        return result;
    }
};

// RAII helper for mock results
class MockResultGuard {
public:
    MockResultGuard(MockPGresult* result) {
        current_mock_result = result;
    }
    
    ~MockResultGuard() {
        mock_result_storage.reset();
        current_mock_result = nullptr;
    }
    
    MockResultGuard(const MockResultGuard&) = delete;
    MockResultGuard& operator=(const MockResultGuard&) = delete;
};

// ============================================================================
// PostgreSQLTest Tests (Alphabetically Ordered)
// ============================================================================

// Tests connect failure
TEST_F(PostgreSQLTest, ConnectFailure) {
    mock_connection_status = CONNECTION_BAD;
    mock_error_message = "Connection refused";

    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "invalid_host";
    params.database = "test_db";

    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests connect success
TEST_F(PostgreSQLTest, ConnectSuccess) {
    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 5432;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";

    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
    EXPECT_EQ("PostgreSQL", conn.get_database_type());
}

// Tests connection retry logic
TEST_F(PostgreSQLTest, ConnectionRetryLogic) {
    // Connection will fail first 2 times, succeed on 3rd
    static int attempt_count = 0;
    
    class RetryableConnection : public PostgreSQLConnection {
    public:
        void connect(const ConnectionParams& params) override {
            attempt_count++;
            if (attempt_count < 3) {
                mock_connection_status = CONNECTION_BAD;
                mock_error_message = "Temporary network failure";
                PostgreSQLConnection::connect(params);
            } else {
                mock_connection_status = CONNECTION_OK;
                mock_error_message = "";
                PostgreSQLConnection::connect(params);
            }
        }
    };
    
    ConnectionRetryPolicy::Config retry_config;
    retry_config.max_attempts = 3;
    retry_config.initial_delay = std::chrono::milliseconds(10);
    retry_config.backoff_multiplier = 2.0;
    retry_config.add_jitter = false;
    
    ConnectionRetryPolicy retry_policy(retry_config);
    auto logger = omop::common::Logger::get("test");
    
    attempt_count = 0;
    bool connected = retry_policy.execute_with_retry(
        [&]() {
            RetryableConnection conn;
            IDatabaseConnection::ConnectionParams params;
            params.host = "localhost";
            params.database = "test_db";
            conn.connect(params);
        },
        logger
    );
    
    EXPECT_TRUE(connected);
    EXPECT_EQ(3, attempt_count);
}

// Tests connection string builder
TEST_F(PostgreSQLTest, ConnectionStringBuilder) {
    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "db.example.com";
    params.port = 5433;
    params.database = "mydb";
    params.username = "user";
    params.password = "pass";
    params.options["sslmode"] = "require";
    params.options["connect_timeout"] = "10";

    // Connection string should contain all parameters
    ASSERT_NO_THROW(conn.connect(params));
}

// Tests execute query
TEST_F(PostgreSQLTest, ExecuteQuery) {
    auto result = createMockResult(2, 3);
    MockResultGuard guard(result);
    current_mock_result->field_names = {"id", "name", "value"};
    current_mock_result->field_types = {23, 25, 701}; // INT4, TEXT, FLOAT8
    current_mock_result->data[0] = {"1", "John", "100.5"};
    current_mock_result->data[1] = {"2", "Jane", "200.75"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result_ptr = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result_ptr);

    // Check first row
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(1, std::any_cast<int>(result_ptr->get_value(0)));
    EXPECT_EQ("John", std::any_cast<std::string>(result_ptr->get_value(1)));
    EXPECT_DOUBLE_EQ(100.5, std::any_cast<double>(result_ptr->get_value(2)));

    // Check second row
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(2, std::any_cast<int>(result_ptr->get_value(0)));
    EXPECT_EQ("Jane", std::any_cast<std::string>(result_ptr->get_value(1)));

    // No more rows
    EXPECT_FALSE(result_ptr->next());
}

// Tests get version
TEST_F(PostgreSQLTest, GetVersion) {
    current_mock_result = createMockResult(1, 1);
    current_mock_result->field_names = {"version"};
    current_mock_result->field_types = {25}; // TEXT
    current_mock_result->data[0] = {"PostgreSQL 14.5"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    EXPECT_EQ("PostgreSQL 14.5", conn.get_version());
}

// Tests invalid column access
TEST_F(PostgreSQLTest, InvalidColumnAccess) {
    current_mock_result = createMockResult(1, 2);
    current_mock_result->field_names = {"col1", "col2"};
    current_mock_result->field_types = {23, 23};
    current_mock_result->data[0] = {"1", "2"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test");
    ASSERT_TRUE(result->next());

    // Test invalid index
    EXPECT_THROW(result->get_value(5), DatabaseException);
    EXPECT_THROW(result->column_name(5), DatabaseException);

    // Test invalid column name
    EXPECT_THROW(result->get_value("nonexistent"), DatabaseException);
}

// Tests network failure scenarios
TEST_F(PostgreSQLTest, NetworkFailureScenarios) {
    // Simulate various network failures
    std::vector<std::pair<ConnStatusType, std::string>> failure_scenarios = {
        {CONNECTION_BAD, "Network unreachable"},
        {CONNECTION_BAD, "Connection timed out"},
        {CONNECTION_BAD, "Connection refused"},
        {CONNECTION_BAD, "Host not found"},
        {CONNECTION_BAD, "SSL connection failed"}
    };
    
    for (const auto& [status, error] : failure_scenarios) {
        mock_connection_status = status;
        mock_error_message = error;
        
        PostgreSQLConnection conn;
        IDatabaseConnection::ConnectionParams params;
        params.host = "test_host";
        params.database = "test_db";
        
        try {
            conn.connect(params);
            FAIL() << "Expected exception for: " << error;
        } catch (const DatabaseException& e) {
            std::string error_str = e.what();
            EXPECT_NE(error_str.find(error), std::string::npos)
                << "Error message should contain: " << error;
        }
    }
}

// Tests null value handling
TEST_F(PostgreSQLTest, NullValueHandling) {
    current_mock_result = createMockResult(1, 3);
    current_mock_result->field_names = {"id", "name", "optional_value"};
    current_mock_result->field_types = {23, 25, 23};
    current_mock_result->data[0] = {"1", "Test", ""};
    current_mock_result->is_null[0][2] = true; // Third column is NULL

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());

    EXPECT_FALSE(result->is_null(0));
    EXPECT_FALSE(result->is_null(1));
    EXPECT_TRUE(result->is_null(2));

    EXPECT_FALSE(result->is_null("id"));
    EXPECT_FALSE(result->is_null("name"));
    EXPECT_TRUE(result->is_null("optional_value"));

    // NULL value should return empty any
    auto null_value = result->get_value(2);
    EXPECT_FALSE(null_value.has_value());
}

// Tests PostgreSQL extractor
TEST_F(PostgreSQLTest, PostgreSQLExtractor) {
    current_mock_result = createMockResult(0, 0);

    auto conn = std::make_unique<PostgreSQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);

    PostgreSQLExtractor extractor(std::move(conn));
    EXPECT_EQ("postgresql", extractor.get_type());
}

// Tests prepared statement
TEST_F(PostgreSQLTest, PreparedStatement) {
    // First call for prepare
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = $1");
    ASSERT_NE(nullptr, stmt);

    // Bind parameter
    stmt->bind(1, 12345LL);

    // Set up result for execution
    current_mock_result = createMockResult(1, 2);
    current_mock_result->field_names = {"person_id", "birth_date"};
    current_mock_result->field_types = {20, 1082};
    current_mock_result->data[0] = {"12345", "2000-01-15"};

    auto result = stmt->execute_query();
    ASSERT_NE(nullptr, result);
    ASSERT_TRUE(result->next());
    EXPECT_EQ(12345LL, std::any_cast<long long>(result->get_value(0)));
}

// Tests prepared statement multiple params
TEST_F(PostgreSQLTest, PreparedStatementMultipleParams) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement(
        "INSERT INTO person (person_id, gender_concept_id, birth_datetime) VALUES ($1, $2, $3)");

    // Bind multiple parameters of different types
    stmt->bind(1, 99999LL);
    stmt->bind(2, 8507);

    auto now = std::chrono::system_clock::now();
    stmt->bind(3, now);

    // Execute update
    current_mock_result = createMockResult(1, 0);
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests query timeout
TEST_F(PostgreSQLTest, QueryTimeout) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Set timeout
    ASSERT_NO_THROW(conn.set_query_timeout(30));
}

// Tests query timeout and cancellation
TEST_F(PostgreSQLTest, QueryTimeoutAndCancellation) {
    // Mock a long-running query scenario
    class SlowResultSet : public MockPGresult {
    public:
        SlowResultSet() {
            ntuples = 1000000;  // Large result set
            nfields = 3;
        }
    };
    
    current_mock_result = new SlowResultSet();
    MockResultGuard guard(current_mock_result);
    
    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Set a short timeout
    conn.set_query_timeout(1);  // 1 second
    
    // This would timeout in a real scenario
    // In our mock, we can't easily simulate the timeout,
    // but we verify the timeout was set
    EXPECT_NO_THROW({
        auto result = conn.execute_query("SELECT * FROM large_table");
    });
}

// Tests record conversion
TEST_F(PostgreSQLTest, RecordConversion) {
    current_mock_result = createMockResult(1, 4);
    current_mock_result->field_names = {"id", "name", "active", "created_at"};
    current_mock_result->field_types = {23, 25, 16, 1114}; // INT, TEXT, BOOL, TIMESTAMP
    current_mock_result->data[0] = {"42", "Test Record", "t", "2024-01-15 10:30:00"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());

    // Convert to record
    omop::core::Record record = result->to_record();

    EXPECT_EQ(42, std::any_cast<int>(record.getField("id")));
    EXPECT_EQ("Test Record", std::any_cast<std::string>(record.getField("name")));
    EXPECT_TRUE(std::any_cast<bool>(record.getField("active")));
    auto created_at_field = record.getFieldOptional("created_at");
    EXPECT_TRUE(created_at_field.has_value());
    EXPECT_TRUE(created_at_field->type() == typeid(std::chrono::system_clock::time_point));
}

// Tests result set columns
TEST_F(PostgreSQLTest, ResultSetColumns) {
    auto result = createMockResult(1, 3);
    MockResultGuard guard(result);
    current_mock_result->field_names = {"person_id", "birth_date", "gender_concept_id"};
    current_mock_result->field_types = {20, 1082, 23}; // BIGINT, DATE, INTEGER
    current_mock_result->data[0] = {"12345", "2000-01-15", "8507"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result_ptr = conn.execute_query("SELECT * FROM person LIMIT 1");

    // Test column count and names
    EXPECT_EQ(3, result_ptr->column_count());
    EXPECT_EQ("person_id", result_ptr->column_name(0));
    EXPECT_EQ("birth_date", result_ptr->column_name(1));
    EXPECT_EQ("gender_concept_id", result_ptr->column_name(2));

    // Test column types
    EXPECT_EQ("bigint", result_ptr->column_type(0));
    EXPECT_EQ("date", result_ptr->column_type(1));
    EXPECT_EQ("integer", result_ptr->column_type(2));

    // Test getting values by column name
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(12345LL, std::any_cast<long long>(result_ptr->get_value("person_id")));
    EXPECT_EQ(8507, std::any_cast<int>(result_ptr->get_value("gender_concept_id")));
}

// Tests table exists
TEST_F(PostgreSQLTest, TableExists) {
    // Set up result for table existence query
    current_mock_result = createMockResult(1, 1);
    current_mock_result->field_names = {"exists"};
    current_mock_result->field_types = {16}; // BOOL
    current_mock_result->data[0] = {"t"}; // true

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    EXPECT_TRUE(conn.table_exists("person"));

    // Test non-existent table
    current_mock_result->data[0] = {"f"}; // false
    EXPECT_FALSE(conn.table_exists("nonexistent_table"));
}

// Tests transactions
TEST_F(PostgreSQLTest, Transactions) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());

    // Execute some operations
    conn.execute_update("INSERT INTO test VALUES (1, 'test')");

    // Commit
    ASSERT_NO_THROW(conn.commit());

    // Begin another transaction
    conn.begin_transaction();

    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

// Define static member
std::atomic<int> PostgreSQLTest::statement_counter_;

File tests/unit/extract/uk_localization_comprehensive_test.cpp:

/**
 * @file uk_localization_comprehensive_test.cpp
 * @brief Comprehensive unit tests for UK localization features in extract library
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains comprehensive tests for UK regional differences including:
 * - Date/time formats (DD/MM/YYYY vs MM/DD/YYYY)
 * - Currency formats (£ symbol, pence handling)
 * - Temperature units (Celsius vs Fahrenheit)
 * - Postal codes (UK format validation)
 * - Telephone numbers (UK format validation)
 * - Measurement units (metric vs imperial)
 * - Address formats
 * - Number formatting (decimal separators, thousands separators)
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/extract_utils.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <locale>
#include <iomanip>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;

// ============================================================================
// UK Localization Comprehensive Tests
// ============================================================================

class UKLocalizationTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_uk_localization_test";
        std::filesystem::create_directories(test_dir_);
        
        // Set UK locale for tests
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            // Fallback if locale not available
            std::locale::global(std::locale("C"));
        }
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests UK date format parsing (DD/MM/YYYY)
TEST_F(UKLocalizationTest, UKDateFormatParsing) {
    CsvOptions options;
    options.date_format = "%d/%m/%Y";
    options.datetime_format = "%d/%m/%Y %H:%M:%S";
    options.has_header = true;
    
    CsvFieldParser parser(options);
    
    // Test various UK date formats
    std::vector<std::string> uk_dates = {
        "15/01/2025",       // Standard UK date
        "01/01/2025",       // New Year's Day
        "31/12/2024",       // New Year's Eve
        "29/02/2024",       // Leap year date
        "25/12/2024",       // Christmas Day
        "04/07/2025",       // Should be 4th July, not April 7th
        "11/09/2024"        // Should be 11th September, not November 9th
    };
    
    for (const auto& date_str : uk_dates) {
        auto date_value = parser.convert_field(date_str, "date");
        EXPECT_TRUE(date_value.has_value()) << "Failed to parse UK date: " << date_str;
        EXPECT_TRUE(date_value.type() == typeid(std::chrono::system_clock::time_point)) 
            << "Date should be parsed as time_point for: " << date_str;
    }
    
    // Test UK datetime format with 24-hour time
    std::vector<std::string> uk_datetimes = {
        "15/01/2025 09:30:00",  // Morning
        "15/01/2025 13:45:30",  // Afternoon (24-hour format)
        "15/01/2025 23:59:59",  // Late evening
        "01/01/2025 00:00:00"   // Midnight
    };
    
    for (const auto& datetime_str : uk_datetimes) {
        auto datetime_value = parser.convert_field(datetime_str, "datetime");
        EXPECT_TRUE(datetime_value.has_value()) << "Failed to parse UK datetime: " << datetime_str;
        EXPECT_TRUE(datetime_value.type() == typeid(std::chrono::system_clock::time_point))
            << "DateTime should be parsed as time_point for: " << datetime_str;
    }
}

// Tests UK currency format handling (£ symbol, pence)
TEST_F(UKLocalizationTest, UKCurrencyFormatHandling) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK currency formats
    std::vector<std::pair<std::string, std::string>> uk_currencies = {
        {"£0.99", "Ninety-nine pence"},
        {"£1.00", "One pound"},
        {"£12.50", "Twelve pounds fifty pence"},
        {"£1,234.56", "Thousands separator with pounds"},
        {"£10,000.00", "Ten thousand pounds"},
        {"£999,999.99", "Just under one million"},
        {"£1,000,000.00", "One million pounds"},
        {"£0.01", "One penny"},
        {"£0.02", "Two pence"},
        {"£5.00", "Five pound note"},
        {"£10.00", "Ten pound note"},
        {"£20.00", "Twenty pound note"},
        {"£50.00", "Fifty pound note"}
    };
    
    for (const auto& [currency_str, description] : uk_currencies) {
        auto currency_value = parser.convert_field(currency_str, "string");
        EXPECT_TRUE(currency_value.has_value()) << "Failed to parse UK currency: " << currency_str;
        EXPECT_EQ(currency_str, std::any_cast<std::string>(currency_value))
            << "Currency should be preserved as string for: " << description;
    }
    
    // Test CSV with UK currency data
    std::string csv_content = R"(item,price,category
Coffee,£2.50,Beverages
Sandwich,£4.95,Food
Newspaper,£1.20,Media
Magazine,£3.99,Media
Bus Ticket,£2.30,Transport
Train Ticket,£15.60,Transport
)";
    
    std::string csv_file = createTestFile("uk_prices.csv", csv_content);
    auto records = omop::extract::utils::extract_csv(csv_file, options);
    
    EXPECT_EQ(6, records.size());
    
    // Verify currency fields are preserved correctly
    EXPECT_TRUE(records[0].hasField("price"));
    auto coffee_price = records[0].getField("price");
    if (coffee_price.type() == typeid(std::string)) {
        EXPECT_EQ("£2.50", std::any_cast<std::string>(coffee_price));
    }
}

// Tests UK temperature format (Celsius)
TEST_F(UKLocalizationTest, UKTemperatureFormat) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK temperature formats (Celsius)
    std::vector<std::pair<std::string, std::string>> uk_temperatures = {
        {"5°C", "Winter temperature"},
        {"15°C", "Mild spring temperature"},
        {"23°C", "Pleasant summer temperature"},
        {"30°C", "Hot summer day"},
        {"-2°C", "Below freezing"},
        {"0°C", "Freezing point"},
        {"37°C", "Body temperature"},
        {"100°C", "Boiling point of water"}
    };
    
    for (const auto& [temp_str, description] : uk_temperatures) {
        auto temp_value = parser.convert_field(temp_str, "string");
        EXPECT_TRUE(temp_value.has_value()) << "Failed to parse UK temperature: " << temp_str;
        EXPECT_EQ(temp_str, std::any_cast<std::string>(temp_value))
            << "Temperature should be preserved as string for: " << description;
    }
    
    // Test CSV with UK weather data
    std::string weather_csv = R"(city,temperature,humidity,condition
London,15°C,78%,Cloudy
Manchester,12°C,82%,Rainy
Birmingham,18°C,65%,Partly Cloudy
Edinburgh,8°C,85%,Overcast
Cardiff,16°C,70%,Sunny
)";
    
    std::string weather_file = createTestFile("uk_weather.csv", weather_csv);
    auto weather_records = omop::extract::utils::extract_csv(weather_file, CsvOptions{});
    
    EXPECT_EQ(5, weather_records.size());
    
    // Verify temperature field preservation
    EXPECT_TRUE(weather_records[0].hasField("temperature"));
    auto london_temp = weather_records[0].getField("temperature");
    if (london_temp.type() == typeid(std::string)) {
        EXPECT_EQ("15°C", std::any_cast<std::string>(london_temp));
    }
}

// Tests UK postal code format validation
TEST_F(UKLocalizationTest, UKPostalCodeValidation) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various valid UK postal codes
    std::vector<std::pair<std::string, std::string>> uk_postcodes = {
        {"SW1A 1AA", "Buckingham Palace"},
        {"M1 1AA", "Manchester"},
        {"B33 8TH", "Birmingham"},
        {"W1A 0AX", "BBC Broadcasting House"},
        {"EC1A 1BB", "City of London"},
        {"E1 6AN", "Tower of London area"},
        {"NW1 6XE", "Camden area"},
        {"SE1 9RT", "South London"},
        {"N1 9GU", "North London"},
        {"WC2N 5DU", "Covent Garden"},
        {"CR0 2YY", "Croydon"},
        {"BR1 3XX", "Bromley"},
        {"TW1 3QS", "Twickenham"},
        {"KT1 2EE", "Kingston upon Thames"},
        {"SM1 1AA", "Sutton"}
    };
    
    for (const auto& [postcode, area] : uk_postcodes) {
        auto postcode_value = parser.convert_field(postcode, "string");
        EXPECT_TRUE(postcode_value.has_value()) << "Failed to parse UK postcode: " << postcode;
        EXPECT_EQ(postcode, std::any_cast<std::string>(postcode_value))
            << "Postcode should be preserved for area: " << area;
    }
    
    // Test CSV with UK address data
    std::string address_csv = R"(name,street,city,postcode,country
John Smith,"123 High Street",London,SW1A 1AA,United Kingdom
Jane Doe,"456 Market Street",Manchester,M1 1AA,United Kingdom
Bob Johnson,"789 Castle Street",Edinburgh,EH1 2NG,United Kingdom
Alice Brown,"321 Queen Street",Cardiff,CF10 2HQ,United Kingdom
)";
    
    std::string address_file = createTestFile("uk_addresses.csv", address_csv);
    auto address_records = omop::extract::utils::extract_csv(address_file, CsvOptions{});
    
    EXPECT_EQ(4, address_records.size());
    
    // Verify postcode field preservation
    for (size_t i = 0; i < address_records.size(); ++i) {
        EXPECT_TRUE(address_records[i].hasField("postcode"));
        EXPECT_TRUE(address_records[i].hasField("country"));
        
        auto country = address_records[i].getField("country");
        if (country.type() == typeid(std::string)) {
            EXPECT_EQ("United Kingdom", std::any_cast<std::string>(country));
        }
    }
}

// Tests UK telephone number format validation
TEST_F(UKLocalizationTest, UKTelephoneNumberValidation) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK telephone number formats
    std::vector<std::pair<std::string, std::string>> uk_phone_numbers = {
        {"+44 20 7946 0958", "London number with country code"},
        {"020 7946 0958", "London number"},
        {"0161 496 0000", "Manchester number"},
        {"0121 496 0000", "Birmingham number"},
        {"0131 496 0000", "Edinburgh number"},
        {"029 2000 0000", "Cardiff number"},
        {"01273 000000", "Brighton number"},
        {"01223 000000", "Cambridge number"},
        {"01865 000000", "Oxford number"},
        {"01904 000000", "York number"},
        {"07700 900000", "Mobile number"},
        {"07400 123456", "Mobile number"},
        {"0800 123456", "Freephone number"},
        {"0845 123456", "Lo-call number"},
        {"0870 123456", "National rate number"}
    };
    
    for (const auto& [phone_str, description] : uk_phone_numbers) {
        auto phone_value = parser.convert_field(phone_str, "string");
        EXPECT_TRUE(phone_value.has_value()) << "Failed to parse UK phone number: " << phone_str;
        EXPECT_EQ(phone_str, std::any_cast<std::string>(phone_value))
            << "Phone number should be preserved for: " << description;
    }
    
    // Test CSV with UK contact data
    std::string contact_csv = R"(name,office_phone,mobile_phone,department
John Smith,020 7946 0958,07700 900123,Finance
Jane Doe,0161 496 0001,07400 123456,Marketing
Bob Johnson,0131 496 0002,07500 987654,Engineering
Alice Brown,029 2000 0003,07600 555777,Human Resources
)";
    
    std::string contact_file = createTestFile("uk_contacts.csv", contact_csv);
    auto contact_records = omop::extract::utils::extract_csv(contact_file, CsvOptions{});
    
    EXPECT_EQ(4, contact_records.size());
    
    // Verify phone number field preservation
    for (const auto& record : contact_records) {
        EXPECT_TRUE(record.hasField("office_phone"));
        EXPECT_TRUE(record.hasField("mobile_phone"));
    }
}

// Tests UK measurement units (metric system)
TEST_F(UKLocalizationTest, UKMeasurementUnits) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK measurement formats (metric system)
    std::vector<std::pair<std::string, std::string>> uk_measurements = {
        {"1.75m", "Height in metres"},
        {"70kg", "Weight in kilograms"},
        {"180cm", "Height in centimetres"},
        {"500ml", "Volume in millilitres"},
        {"2.5l", "Volume in litres"},
        {"250g", "Weight in grams"},
        {"1.5km", "Distance in kilometres"},
        {"50mm", "Length in millimetres"},
        {"10°C", "Temperature in Celsius"},
        {"750ml", "Wine bottle volume"},
        {"330ml", "Beer can volume"},
        {"2L", "Large bottle volume"},
        {"100g", "Food portion weight"}
    };
    
    for (const auto& [measurement_str, description] : uk_measurements) {
        auto measurement_value = parser.convert_field(measurement_str, "string");
        EXPECT_TRUE(measurement_value.has_value()) << "Failed to parse UK measurement: " << measurement_str;
        EXPECT_EQ(measurement_str, std::any_cast<std::string>(measurement_value))
            << "Measurement should be preserved for: " << description;
    }
    
    // Test CSV with UK product data
    std::string product_csv = R"(product,weight,volume,price,category
Milk,1kg,1l,£1.20,Dairy
Orange Juice,500g,500ml,£2.50,Beverages
Bread,800g,N/A,£1.50,Bakery
Wine,750g,750ml,£8.99,Alcohol
Beer,330g,330ml,£1.75,Alcohol
)";
    
    std::string product_file = createTestFile("uk_products.csv", product_csv);
    auto product_records = omop::extract::utils::extract_csv(product_file, CsvOptions{});
    
    EXPECT_EQ(5, product_records.size());
    
    // Verify measurement fields are preserved
    for (const auto& record : product_records) {
        EXPECT_TRUE(record.hasField("weight"));
        EXPECT_TRUE(record.hasField("volume"));
        EXPECT_TRUE(record.hasField("price"));
    }
}

// Tests UK number formatting (decimal separator, thousands separator)
TEST_F(UKLocalizationTest, UKNumberFormatting) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK number formats
    std::vector<std::pair<std::string, std::string>> uk_numbers = {
        {"1,234", "Thousands separator"},
        {"12,345", "Ten thousands"},
        {"123,456", "Hundreds of thousands"},
        {"1,234,567", "Millions"},
        {"1,234,567.89", "Millions with decimal"},
        {"123.45", "Decimal number"},
        {"0.99", "Less than one"},
        {"1000", "Thousand without separator"},
        {"3.14159", "Pi approximation"},
        {"2.718", "Euler's number approximation"}
    };
    
    for (const auto& [number_str, description] : uk_numbers) {
        auto number_value = parser.convert_field(number_str, "");
        EXPECT_TRUE(number_value.has_value()) << "Failed to parse UK number: " << number_str;
        // Numbers should be auto-detected as numeric types or preserved as strings
    }
    
    // Test CSV with UK financial data
    std::string financial_csv = R"(account,balance,transactions,interest_rate
Savings,£12,345.67,145,2.5%
Current,£1,234.56,89,0.1%
ISA,£20,000.00,52,3.2%
Premium,£50,000.00,23,1.8%
)";
    
    std::string financial_file = createTestFile("uk_financial.csv", financial_csv);
    auto financial_records = omop::extract::utils::extract_csv(financial_file, CsvOptions{});
    
    EXPECT_EQ(4, financial_records.size());
    
    // Verify financial fields are preserved
    for (const auto& record : financial_records) {
        EXPECT_TRUE(record.hasField("balance"));
        EXPECT_TRUE(record.hasField("transactions"));
        EXPECT_TRUE(record.hasField("interest_rate"));
    }
}

// Tests comprehensive UK localized CSV extraction
TEST_F(UKLocalizationTest, ComprehensiveUKLocalizedCSVExtraction) {
    // Create a comprehensive UK-localized CSV file
    std::string comprehensive_csv = R"(patient_id,name,dob,address,postcode,phone,weight,height,temperature,visit_date,cost
1001,John Smith,15/01/1980,"123 High Street, London",SW1A 1AA,020 7946 0958,75kg,1.75m,36.5°C,25/12/2024,£150.00
1002,Jane Doe,03/07/1975,"456 Market Street, Manchester",M1 1AA,0161 496 0000,65kg,1.68m,37.0°C,26/12/2024,£125.50
1003,Bob Johnson,22/11/1990,"789 Castle Street, Edinburgh",EH1 2NG,0131 496 0000,80kg,1.82m,36.8°C,27/12/2024,£175.25
1004,Alice Brown,08/05/1985,"321 Queen Street, Cardiff",CF10 2HQ,029 2000 0000,60kg,1.65m,36.2°C,28/12/2024,£200.75
)";
    
    // Configure CSV options for UK formats
    CsvOptions uk_options;
    uk_options.date_format = "%d/%m/%Y";
    uk_options.datetime_format = "%d/%m/%Y %H:%M:%S";
    uk_options.has_header = true;
    uk_options.delimiter = ',';
    uk_options.trim_fields = true;
    
    std::string comprehensive_file = createTestFile("uk_patients.csv", comprehensive_csv);
    auto patient_records = omop::extract::utils::extract_csv(comprehensive_file, uk_options);
    
    EXPECT_EQ(4, patient_records.size());
    
    // Verify all UK-specific fields are properly extracted
    for (size_t i = 0; i < patient_records.size(); ++i) {
        const auto& record = patient_records[i];
        
        // Check all expected fields exist
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("name"));
        EXPECT_TRUE(record.hasField("dob"));
        EXPECT_TRUE(record.hasField("address"));
        EXPECT_TRUE(record.hasField("postcode"));
        EXPECT_TRUE(record.hasField("phone"));
        EXPECT_TRUE(record.hasField("weight"));
        EXPECT_TRUE(record.hasField("height"));
        EXPECT_TRUE(record.hasField("temperature"));
        EXPECT_TRUE(record.hasField("visit_date"));
        EXPECT_TRUE(record.hasField("cost"));
        
        // Verify UK postcode format is preserved
        auto postcode = record.getField("postcode");
        if (postcode.type() == typeid(std::string)) {
            std::string postcode_str = std::any_cast<std::string>(postcode);
            EXPECT_TRUE(postcode_str.find(" ") != std::string::npos) 
                << "UK postcode should contain space: " << postcode_str;
        }
        
        // Verify UK currency format is preserved
        auto cost = record.getField("cost");
        if (cost.type() == typeid(std::string)) {
            std::string cost_str = std::any_cast<std::string>(cost);
            EXPECT_TRUE(cost_str.starts_with("£")) 
                << "UK cost should start with £: " << cost_str;
        }
        
        // Verify UK phone number format is preserved
        auto phone = record.getField("phone");
        if (phone.type() == typeid(std::string)) {
            std::string phone_str = std::any_cast<std::string>(phone);
            EXPECT_TRUE(phone_str.starts_with("0") || phone_str.starts_with("+44"))
                << "UK phone should start with 0 or +44: " << phone_str;
        }
    }
}

// Tests JSON extraction with UK localized data
TEST_F(UKLocalizationTest, UKLocalizedJSONExtraction) {
    // Create a JSON file with UK-specific data
    std::string uk_json = R"([
  {
    "patient_id": 1001,
    "name": "John Smith",
    "dob": "15/01/1980",
    "address": {
      "street": "123 High Street",
      "city": "London",
      "postcode": "SW1A 1AA",
      "country": "United Kingdom"
    },
    "contact": {
      "phone": "020 7946 0958",
      "mobile": "07700 900123"
    },
    "measurements": {
      "weight": "75kg",
      "height": "1.75m",
      "temperature": "36.5°C"
    },
    "visit": {
      "date": "25/12/2024",
      "cost": "£150.00"
    }
  },
  {
    "patient_id": 1002,
    "name": "Jane Doe",
    "dob": "03/07/1975",
    "address": {
      "street": "456 Market Street",
      "city": "Manchester",
      "postcode": "M1 1AA",
      "country": "United Kingdom"
    },
    "contact": {
      "phone": "0161 496 0000",
      "mobile": "07400 123456"
    },
    "measurements": {
      "weight": "65kg",
      "height": "1.68m",
      "temperature": "37.0°C"
    },
    "visit": {
      "date": "26/12/2024",
      "cost": "£125.50"
    }
  }
])";
    
    JsonOptions uk_json_options;
    uk_json_options.flatten_nested = true;
    uk_json_options.parse_dates = true;
    uk_json_options.date_formats = {"%d/%m/%Y", "%d/%m/%Y %H:%M:%S"};
    
    std::string json_file = createTestFile("uk_patients.json", uk_json);
    auto json_records = omop::extract::utils::extract_json(json_file, uk_json_options);
    
    EXPECT_EQ(2, json_records.size());
    
    // Verify flattened UK-specific fields are properly extracted
    for (const auto& record : json_records) {
        // Check flattened address fields
        EXPECT_TRUE(record.hasField("address_postcode") || record.hasField("address.postcode"));
        EXPECT_TRUE(record.hasField("address_country") || record.hasField("address.country"));
        
        // Check flattened contact fields
        EXPECT_TRUE(record.hasField("contact_phone") || record.hasField("contact.phone"));
        EXPECT_TRUE(record.hasField("contact_mobile") || record.hasField("contact.mobile"));
        
        // Check flattened measurement fields
        EXPECT_TRUE(record.hasField("measurements_weight") || record.hasField("measurements.weight"));
        EXPECT_TRUE(record.hasField("measurements_height") || record.hasField("measurements.height"));
        EXPECT_TRUE(record.hasField("measurements_temperature") || record.hasField("measurements.temperature"));
        
        // Check flattened visit fields
        EXPECT_TRUE(record.hasField("visit_cost") || record.hasField("visit.cost"));
        EXPECT_TRUE(record.hasField("visit_date") || record.hasField("visit.date"));
    }
}

File tests/unit/extract/compressed_csv_test.cpp:

/**
 * @file compressed_csv_extractor_test.cpp
 * @brief Unit tests for CompressedCsvExtractor class
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <zlib.h>

namespace omop::extract::test {

using namespace ::testing;

// Helper class to create test files
class CompressedCsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = std::filesystem::temp_directory_path() / "compressed_csv_test";
        std::filesystem::create_directories(test_dir_);

        // Create sample CSV content
        csv_content_ = "id,name,value\n1,test1,100\n2,test2,200\n3,test3,300\n";
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Create a gzip compressed file
    std::string CreateGzipFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        gzFile gz = gzopen(filepath.c_str(), "wb");
        EXPECT_NE(gz, nullptr);

        int written = gzwrite(gz, content.c_str(), content.length());
        EXPECT_EQ(written, content.length());

        gzclose(gz);
        return filepath;
    }

    // Create an uncompressed CSV file
    std::string CreateCsvFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath;
    }

    std::filesystem::path test_dir_;
    std::string csv_content_;
};

// ============================================================================
// CompressedCsvExtractorTest Tests
// ============================================================================

// Tests batch extraction with limit
TEST_F(CompressedCsvExtractorTest, BatchExtractionWithLimit) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["max_records"] = size_t(2);

    extractor.initialize(config, context);

    // Extract with limit
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 2); // Should respect max_records limit

    // Verify extracted data
    auto records = batch.getRecords();
    EXPECT_EQ(records.size(), 2);

    // Check first record
    EXPECT_EQ(std::any_cast<long long>(records[0].getField("id")), 1);
    EXPECT_EQ(std::any_cast<std::string>(records[0].getField("name")), "test1");

    // Check second record
    EXPECT_EQ(std::any_cast<long long>(records[1].getField("id")), 2);
    EXPECT_EQ(std::any_cast<std::string>(records[1].getField("name")), "test2");
}

// Tests cleanup temporary files
TEST_F(CompressedCsvExtractorTest, CleanupTemporaryFiles) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = true;

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Get temp file path before finalization
    auto stats = extractor.get_statistics();

    // Finalize should clean up temp file
    extractor.finalize(context);

    // Verify temp file is cleaned up
    // Note: We can't directly check the temp file as it's private,
    // but we can verify the extractor completes without errors
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Tests corrupted compressed file
TEST_F(CompressedCsvExtractorTest, CorruptedCompressedFile) {
    // Create a corrupted gzip file with invalid magic numbers
    std::string filepath = (test_dir_ / "corrupted.csv.gz").string();
    std::ofstream file(filepath, std::ios::binary);
    // Write some bytes that don't match gzip magic numbers (0x1f, 0x8b)
    file.write("\x00\x00", 2); // Definitely not gzip magic
    file << "invalid gzip content";
    file.close();

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = true;

    // Should throw an exception for corrupted file
    EXPECT_THROW(extractor.initialize(config, context), omop::common::ExtractionException);
}

// Tests detect compression by extension
TEST_F(CompressedCsvExtractorTest, DetectCompressionByExtension) {
    struct TestCase {
        std::string filename;
        CompressedCsvExtractor::CompressionFormat expected;
    };

    std::vector<TestCase> test_cases = {
        {"test.csv.gz", CompressedCsvExtractor::CompressionFormat::Gzip},
        {"test.csv.zip", CompressedCsvExtractor::CompressionFormat::Zip},
        {"test.csv.bz2", CompressedCsvExtractor::CompressionFormat::Bzip2},
        {"test.csv.xz", CompressedCsvExtractor::CompressionFormat::Xz},
        {"test.csv", CompressedCsvExtractor::CompressionFormat::None}
    };

    for (const auto& tc : test_cases) {
        std::string filepath = (test_dir_ / tc.filename).string();
        std::ofstream(filepath) << "dummy";

        // Note: detect_compression is protected, so we can't test it directly
        // CompressedCsvExtractor extractor;
        // auto format = extractor.detect_compression(filepath);
        // EXPECT_EQ(format, tc.expected) << "Failed for file: " << tc.filename;
    }
}

// Tests disable cleanup
TEST_F(CompressedCsvExtractorTest, DisableCleanup) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = false; // Disable cleanup

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Finalize without cleanup
    extractor.finalize(context);

    // Should still work normally
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Tests extract from compressed file
TEST_F(CompressedCsvExtractorTest, ExtractFromCompressedFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    extractor.initialize(config, context);

    // Extract records
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Verify extracted data
    auto records = batch.getRecords();
    EXPECT_EQ(records.size(), 3);

    // Check first record
    const auto& field_id = records[0].getField("id");
    EXPECT_EQ(std::any_cast<long long>(field_id), 1);

    const auto& field_name = records[0].getField("name");
    EXPECT_EQ(std::any_cast<std::string>(field_name), "test1");
}

// Tests explicit compression format
TEST_F(CompressedCsvExtractorTest, ExplicitCompressionFormat) {
    // Create file with misleading extension
    std::string gz_file = CreateGzipFile(csv_content_, "test.dat");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["compression_format"] = std::string("gzip");

    EXPECT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests get type
TEST_F(CompressedCsvExtractorTest, GetType) {
    CompressedCsvExtractor extractor;
    EXPECT_EQ(extractor.get_type(), "compressed_csv");
}

// Tests handle non compressed file
TEST_F(CompressedCsvExtractorTest, HandleNonCompressedFile) {
    std::string csv_file = CreateCsvFile(csv_content_, "test.csv");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file;
    config["has_header"] = true;

    // Should work like regular CSV extractor
    EXPECT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests initialize with gzip file
TEST_F(CompressedCsvExtractorTest, InitializeWithGzipFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    EXPECT_NO_THROW(extractor.initialize(config, context));
    EXPECT_TRUE(extractor.has_more_data());
}

// Tests large compressed file
TEST_F(CompressedCsvExtractorTest, LargeCompressedFile) {
    // Create large CSV content
    std::string large_csv = "id,name,value\n";
    for (int i = 1; i <= 1000; ++i) {
        large_csv += std::to_string(i) + ",test" + std::to_string(i) + "," + std::to_string(i * 100) + "\n";
    }

    std::string gz_file = CreateGzipFile(large_csv, "large.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["batch_size"] = size_t(100);

    extractor.initialize(config, context);

    // Extract in batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(100, context);
        total_records += batch.size();
    }

    EXPECT_EQ(total_records, 1000);
}

// Tests statistics
TEST_F(CompressedCsvExtractorTest, Statistics) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Get statistics
    auto stats = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_records"]), 3);
    EXPECT_EQ(std::any_cast<size_t>(stats["successful_records"]), 3);
    EXPECT_EQ(std::any_cast<size_t>(stats["failed_records"]), 0);

    // Should contain compression info
    EXPECT_TRUE(stats.find("compression_format") != stats.end());
    EXPECT_TRUE(stats.find("original_size") != stats.end());
    EXPECT_TRUE(stats.find("compressed_size") != stats.end());
}

} // namespace omop::extract::test


File tests/unit/extract/json_extractor_test.cpp:

/**
 * @file json_extractor_test.cpp
 * @brief Unit tests for JSON extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/json_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <iostream>
#include "extract/extractor_factory.h"

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using json = nlohmann::json;

// Test fixture for JSON extractor tests
class JsonExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_json_test";
        std::filesystem::create_directories(test_dir_);
        
        // Register JSON extractors with the factory registry
        JsonExtractorFactory::register_extractors();
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test JSON file
    std::string createTestJson(const std::string& filename, const json& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content.dump(2);
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// ============================================================================
// JsonExtractorTest Tests
// ============================================================================

// Tests detailed error reporting
TEST_F(JsonExtractorTest, DetailedErrorReporting) {
    // Create invalid JSON file
    std::string filepath = (test_dir_ / "invalid.json").string();
    std::ofstream file(filepath);
    file << "{\"invalid\": json}"; // Invalid JSON
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests extract JSON array
TEST_F(JsonExtractorTest, ExtractJsonArray) {
    json content = json::array({
        {{"name", "John"}, {"age", 30}, {"city", "New York"}},
        {{"name", "Jane"}, {"age", 25}, {"city", "Los Angeles"}}
    });

    std::string filepath = createTestJson("array.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("name")));
    EXPECT_EQ(30LL, std::any_cast<int64_t>(records[0].getField("age")));
}

// Tests extract single object
TEST_F(JsonExtractorTest, ExtractSingleObject) {
    json content = {
        {"id", 1},
        {"name", "John Doe"},
        {"email", "<EMAIL>"}
    };

    std::string filepath = createTestJson("single.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(1, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("John Doe", std::any_cast<std::string>(records[0].getField("name")));
}

// Tests extract with root path
TEST_F(JsonExtractorTest, ExtractWithRootPath) {
    json content = {
        {"data", {
            {"patients", json::array({
                {{"id", 1}, {"name", "John Doe"}},
                {{"id", 2}, {"name", "Jane Smith"}}
            })}
        }}
    };

    std::string filepath = createTestJson("nested.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["root_path"] = std::string("data.patients");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("Jane Smith", std::any_cast<std::string>(records[1].getField("name")));
}

// Tests extreme limits and boundaries
TEST_F(JsonExtractorTest, ExtremeLimitsAndBoundaries) {
    // Test with very large JSON
    json content = json::array();
    for (int i = 0; i < 1000; ++i) {
        json record = {
            {"id", i},
            {"name", "User" + std::to_string(i)},
            {"data", std::string(1000, 'x')} // Large string
        };
        content.push_back(record);
    }

    std::string filepath = createTestJson("large.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["batch_size"] = size_t(100);

    ProcessingContext context;
    extractor.initialize(config, context);

    // Extract in batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(100, context);
        total_records += batch.size();
    }

    EXPECT_EQ(1000, total_records);
}

// Tests flatten arrays
TEST_F(JsonExtractorTest, FlattenArrays) {
    json content = json::array({
        {
            {"id", 1},
            {"tags", json::array({"tag1", "tag2", "tag3"})},
            {"scores", json::array({85, 90, 95})}
        }
    });

    std::string filepath = createTestJson("array_flatten.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["flatten_nested"] = true;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ("tag1", std::any_cast<std::string>(records[0].getField("tags_0")));
    EXPECT_EQ("tag2", std::any_cast<std::string>(records[0].getField("tags_1")));
    EXPECT_EQ(85LL, std::any_cast<int64_t>(records[0].getField("scores_0")));
}

// Tests flatten nested objects
TEST_F(JsonExtractorTest, FlattenNestedObjects) {
    json content = json::array({
        {
            {"id", 1},
            {"person", {
                {"name", "John"},
                {"address", {
                    {"street", "123 Main St"},
                    {"city", "New York"}
                }}
            }}
        }
    });

    std::string filepath = createTestJson("nested_flatten.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["flatten_nested"] = true;
    config["array_delimiter"] = std::string("_");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("person_name")));
    EXPECT_EQ("123 Main St", std::any_cast<std::string>(records[0].getField("person_address_street")));
    EXPECT_EQ("New York", std::any_cast<std::string>(records[0].getField("person_address_city")));
}

// Tests get statistics
TEST_F(JsonExtractorTest, GetStatistics) {
    json content = json::array({
        {{"id", 1}, {"name", "John"}},
        {{"id", 2}, {"name", "Jane"}},
        {{"id", 3}, {"name", "Bob"}}
    });

    std::string filepath = createTestJson("stats.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto stats = extractor.get_statistics();

    EXPECT_EQ(3, std::any_cast<size_t>(stats["total_records"]));
    EXPECT_EQ(3, std::any_cast<size_t>(stats["successful_records"]));
    EXPECT_EQ(0, std::any_cast<size_t>(stats["failed_records"]));
}

// Tests handle invalid JSON
TEST_F(JsonExtractorTest, HandleInvalidJson) {
    // Create invalid JSON file
    std::string filepath = (test_dir_ / "invalid.json").string();
    std::ofstream file(filepath);
    file << "{\"invalid\": json}"; // Invalid JSON
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests handle missing file
TEST_F(JsonExtractorTest, HandleMissingFile) {
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string("/nonexistent/file.json");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests handle null values
TEST_F(JsonExtractorTest, HandleNullValues) {
    json content = json::array({
        {
            {"id", 1},
            {"name", "John"},
            {"age", nullptr},
            {"city", json::value_t::null}
        }
    });

    std::string filepath = createTestJson("nulls.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("name")));
    // Null values should be handled gracefully
    EXPECT_FALSE(records[0].hasField("age"));
    EXPECT_FALSE(records[0].hasField("city"));
}

// Tests JSON extractor factory
TEST_F(JsonExtractorTest, JsonExtractorFactory) {
    // Test factory registration
    auto extractor = omop::extract::ExtractorFactoryRegistry::create("json");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "json");

    // Test JSONL alias
    auto jsonl_extractor = omop::extract::ExtractorFactoryRegistry::create("jsonl");
    EXPECT_NE(jsonl_extractor, nullptr);
    EXPECT_EQ(jsonl_extractor->get_type(), "jsonl");
}

// Tests JSON lines edge cases
TEST_F(JsonExtractorTest, JsonLinesEdgeCases) {
    // Create JSONL file with various edge cases
    std::string filepath = (test_dir_ / "edges.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "{\"id\": 2, \"name\": \"Jane\", \"extra\": null}\n";
    file << "{\"id\": 3}\n"; // Missing name
    file << "{\"id\": 4, \"nested\": {\"value\": 42}}\n";
    file << "\n"; // Empty line
    file << "{\"id\": 5, \"array\": [1, 2, 3]}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(5, batch.size()); // Should skip empty line
}

// Tests JSON lines extractor
TEST_F(JsonExtractorTest, JsonLinesExtractor) {
    // Create JSONL file
    std::string filepath = (test_dir_ / "test.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "{\"id\": 2, \"name\": \"Jane\"}\n";
    file << "{\"id\": 3, \"name\": \"Bob\"}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(3, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("Jane", std::any_cast<std::string>(records[1].getField("name")));
}

// Tests JSON lines with errors
TEST_F(JsonExtractorTest, JsonLinesWithErrors) {
    // Create JSONL file with invalid lines
    std::string filepath = (test_dir_ / "errors.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "invalid json line\n";
    file << "{\"id\": 2, \"name\": \"Jane\"}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");
    config["continue_on_error"] = true;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size()); // Should skip invalid line
}

// Tests max depth limit
TEST_F(JsonExtractorTest, MaxDepthLimit) {
    // Create deeply nested JSON
    json content = {
        {"level1", {
            {"level2", {
                {"level3", {
                    {"level4", {
                        {"level5", {
                            {"level6", {
                                {"level7", {
                                    {"level8", {
                                        {"level9", {
                                            {"level10", "value"}
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
    };

    std::string filepath = createTestJson("deep.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["max_depth"] = int(5);

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Should not exceed max depth
    EXPECT_FALSE(records[0].hasField("level1_level2_level3_level4_level5_level6"));
}

// Tests parse dates
TEST_F(JsonExtractorTest, ParseDates) {
    json content = json::array({
        {
            {"id", 1},
            {"created_date", "2024-01-15"},
            {"updated_time", "2024-01-15 10:30:45"}
        }
    });

    std::string filepath = createTestJson("dates.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["parse_dates"] = true;
    config["date_formats"] = std::vector<std::string>{"%Y-%m-%d", "%Y-%m-%d %H:%M:%S"};

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    auto date_field = records[0].getFieldOptional("created_date");
    EXPECT_TRUE(date_field->type() == typeid(std::chrono::system_clock::time_point));
}

// Tests streaming JSON extractor
TEST_F(JsonExtractorTest, StreamingJsonExtractor) {
    // Create large JSON file for streaming test
    json content = json::array();
    for (int i = 0; i < 100; ++i) {
        json record = {
            {"id", i},
            {"name", "User" + std::to_string(i)},
            {"data", "Some data for user " + std::to_string(i)}
        };
        content.push_back(record);
    }

    std::string filepath = createTestJson("streaming.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["streaming"] = true;
    config["batch_size"] = size_t(10);

    ProcessingContext context;
    extractor.initialize(config, context);

    // Extract in small batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10, context);
        total_records += batch.size();
        EXPECT_LE(batch.size(), 10); // Should not exceed batch size
    }

    EXPECT_EQ(100, total_records);
}

File tests/unit/extract/mysql_connector_test.cpp:

/**
 * @file mysql_connector_test.cpp
 * @brief Unit tests for MySQL connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifdef OMOP_HAS_MYSQL

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/mysql_connector.h"
#include "common/exceptions.h"
#include <cstring>
#include <vector>
#include <chrono>

using namespace omop::extract;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;

// Define MySQL types and constants for testing
typedef char my_bool;
typedef unsigned long long my_ulonglong;
typedef char** MYSQL_ROW;

enum enum_field_types {
    MYSQL_TYPE_DECIMAL, MYSQL_TYPE_TINY,
    MYSQL_TYPE_SHORT, MYSQL_TYPE_LONG,
    MYSQL_TYPE_FLOAT, MYSQL_TYPE_DOUBLE,
    MYSQL_TYPE_NULL, MYSQL_TYPE_TIMESTAMP,
    MYSQL_TYPE_LONGLONG, MYSQL_TYPE_INT24,
    MYSQL_TYPE_DATE, MYSQL_TYPE_TIME,
    MYSQL_TYPE_DATETIME, MYSQL_TYPE_YEAR,
    MYSQL_TYPE_NEWDATE, MYSQL_TYPE_VARCHAR,
    MYSQL_TYPE_BIT
};

enum enum_mysql_stmt_state {
    MYSQL_STMT_INIT_DONE = 1,
    MYSQL_STMT_PREPARE_DONE,
    MYSQL_STMT_EXECUTE_DONE,
    MYSQL_STMT_FETCH_DONE
};

enum mysql_option {
    MYSQL_OPT_CONNECT_TIMEOUT,
    MYSQL_OPT_COMPRESS,
    MYSQL_OPT_NAMED_PIPE,
    MYSQL_INIT_COMMAND,
    MYSQL_READ_DEFAULT_FILE,
    MYSQL_READ_DEFAULT_GROUP,
    MYSQL_SET_CHARSET_DIR,
    MYSQL_SET_CHARSET_NAME,
    MYSQL_OPT_LOCAL_INFILE,
    MYSQL_OPT_PROTOCOL,
    MYSQL_SHARED_MEMORY_BASE_NAME,
    MYSQL_OPT_READ_TIMEOUT,
    MYSQL_OPT_WRITE_TIMEOUT,
    MYSQL_OPT_USE_RESULT,
    MYSQL_OPT_USE_REMOTE_CONNECTION,
    MYSQL_OPT_USE_EMBEDDED_CONNECTION,
    MYSQL_OPT_GUESS_CONNECTION,
    MYSQL_SET_CLIENT_IP,
    MYSQL_SECURE_AUTH,
    MYSQL_REPORT_DATA_TRUNCATION,
    MYSQL_OPT_RECONNECT,
    MYSQL_OPT_SSL_VERIFY_SERVER_CERT,
    MYSQL_PLUGIN_DIR,
    MYSQL_DEFAULT_AUTH,
    MYSQL_OPT_BIND,
    MYSQL_OPT_SSL_KEY,
    MYSQL_OPT_SSL_CERT,
    MYSQL_OPT_SSL_CA,
    MYSQL_OPT_SSL_CAPATH,
    MYSQL_OPT_SSL_CIPHER,
    MYSQL_OPT_SSL_CRL,
    MYSQL_OPT_SSL_CRLPATH,
    MYSQL_OPT_CONNECT_ATTR_RESET,
    MYSQL_OPT_CONNECT_ATTR_ADD,
    MYSQL_OPT_CONNECT_ATTR_DELETE,
    MYSQL_SERVER_PUBLIC_KEY,
    MYSQL_ENABLE_CLEARTEXT_PLUGIN,
    MYSQL_OPT_CAN_HANDLE_EXPIRED_PASSWORDS
};

#define MYSQL_NO_DATA 100

// ODBC types for compatibility
typedef void* SQLHSTMT;
typedef unsigned short SQLUSMALLINT;
typedef short SQLSMALLINT;
typedef void* SQLPOINTER;
typedef long SQLLEN;
#define SQL_SUCCESS 0

struct MYSQL_FIELD {
    char* name;
    char* org_name;
    char* table;
    char* org_table;
    char* db;
    char* catalog;
    char* def;
    unsigned long length;
    unsigned long max_length;
    unsigned int name_length;
    unsigned int org_name_length;
    unsigned int table_length;
    unsigned int org_table_length;
    unsigned int db_length;
    unsigned int catalog_length;
    unsigned int def_length;
    unsigned int flags;
    unsigned int decimals;
    unsigned int charsetnr;
    enum enum_field_types type;
    void* extension;
};

struct MYSQL_BIND {
    unsigned long* length;
    my_bool* is_null;
    void* buffer;
    my_bool* error;
    unsigned char* row_ptr;
    void (*store_param_func)(void*, MYSQL_BIND*, unsigned char*, unsigned char**);
    void (*fetch_result)(MYSQL_BIND*, MYSQL_FIELD*, unsigned char**);
    void (*skip_result)(MYSQL_BIND*, MYSQL_FIELD*, unsigned char**);
    unsigned long buffer_length;
    unsigned long offset;
    unsigned long length_value;
    unsigned int param_number;
    unsigned int pack_length;
    enum enum_field_types buffer_type;
    my_bool error_value;
    my_bool is_unsigned;
    my_bool long_data_used;
    my_bool is_null_value;
    void* extension;
};

struct MYSQL_DATA {
    struct MYSQL_ROWS* data;
    void* embedded_info;
    void* alloc;
    my_ulonglong rows;
    unsigned int fields;
};

struct MYSQL_ROWS {
    struct MYSQL_ROWS* next;
    MYSQL_ROW data;
    unsigned long length;
};

struct MEM_ROOT {
    void* free;
    void* used;
    void* pre_alloc;
    size_t min_malloc;
    size_t block_size;
    unsigned int block_num;
    unsigned int first_block_usage;
    void (*error_handler)(void);
};

// Forward declarations
struct MYSQL;
struct MYSQL_STMT;
struct MYSQL_RES;

// Mock MySQL structures and functions
struct MockMYSQL {
    unsigned int thread_id;
    char* host;
    char* user;
    char* passwd;
    char* unix_socket;
    char* server_version;
    char* host_info;
    char* info;
    char* db;
    unsigned int port;
    unsigned int client_flag;
    unsigned int server_capabilities;
    unsigned int protocol_version;
    unsigned int field_count;
    unsigned int server_status;
    unsigned int server_language;
    unsigned int warning_count;
};

struct MockMYSQL_STMT {
    MYSQL* mysql;
    unsigned long stmt_id;
    unsigned long flags;
    enum enum_mysql_stmt_state state;
    MYSQL_FIELD* fields;
    unsigned int field_count;
    unsigned int param_count;
    my_bool bind_param_done;
    my_bool bind_result_done;
    MYSQL_BIND* params;
    MYSQL_BIND* bind;
    MYSQL_DATA result;
    my_ulonglong affected_rows;
    my_ulonglong insert_id;
    int mysql_errno;
    char error[512];
    char sqlstate[6];
};

struct MockMYSQL_RES {
    my_ulonglong row_count;
    unsigned int field_count;
    unsigned int current_field;
    MYSQL_FIELD* fields;
    MYSQL_DATA* data;
    MYSQL_ROWS* data_cursor;
    MEM_ROOT field_alloc;
    MYSQL_ROW row;
    MYSQL_ROW current_row;
    unsigned long* lengths;
    MYSQL* handle;
    my_bool eof;
};

// Mock data storage
static MYSQL mock_mysql;
static MYSQL_STMT mock_stmt;
static MYSQL_RES mock_result;
static int mock_errno = 0;
static std::string mock_error = "";
static my_bool mock_connection_status = 1;
static MYSQL_FIELD* mock_fields = nullptr;
static int mock_field_count = 0;
static std::vector<std::vector<std::string>> mock_data;
static int mock_current_row = -1;

// RAII helper for mock fields
class MockFieldsGuard {
public:
    MockFieldsGuard(MYSQL_FIELD* fields, int count) 
        : fields_(fields), count_(count) {
        mock_fields = fields_;
        mock_field_count = count_;
    }
    
    ~MockFieldsGuard() {
        mock_fields = nullptr;
        mock_field_count = 0;
        delete[] fields_;
    }
    
    MockFieldsGuard(const MockFieldsGuard&) = delete;
    MockFieldsGuard& operator=(const MockFieldsGuard&) = delete;
    
private:
    MYSQL_FIELD* fields_;
    int count_;
};

// Mock MySQL C API functions
extern "C" {
    MYSQL* mysql_init(MYSQL*) {
        memset(&mock_mysql, 0, sizeof(mock_mysql));
        return &mock_mysql;
    }

    void mysql_close(MYSQL*) {}

    MYSQL* mysql_real_connect(MYSQL* mysql, const char* host, const char* user,
                             const char* passwd, const char* db, unsigned int port,
                             const char* unix_socket, unsigned long client_flag) {
        return mock_connection_status ? mysql : nullptr;
    }

    const char* mysql_error(MYSQL*) {
        return mock_error.c_str();
    }

    unsigned int mysql_errno(MYSQL*) {
        return mock_errno;
    }

    int mysql_ping(MYSQL*) {
        return mock_connection_status ? 0 : 1;
    }

    int mysql_set_character_set(MYSQL*, const char*) {
        return 0;
    }

    const char* mysql_get_server_info(MYSQL*) {
        return "8.0.32";
    }

    int mysql_query(MYSQL*, const char*) {
        return mock_errno;
    }

    my_ulonglong mysql_affected_rows(MYSQL*) {
        return mock_data.size();
    }

    MYSQL_STMT* mysql_stmt_init(MYSQL* mysql) {
        memset(&mock_stmt, 0, sizeof(mock_stmt));
        mock_stmt.mysql = mysql;
        return &mock_stmt;
    }

    int mysql_stmt_prepare(MYSQL_STMT* stmt, const char* query, unsigned long length) {
        stmt->param_count = 0;
        // Count parameters (simple implementation)
        const char* p = query;
        while ((p = strstr(p, "?")) != nullptr) {
            stmt->param_count++;
            p++;
        }
        return 0;
    }

    int mysql_stmt_execute(MYSQL_STMT*) {
        return 0;
    }

    int mysql_stmt_close(MYSQL_STMT*) {
        return 0;
    }

    const char* mysql_stmt_error(MYSQL_STMT*) {
        return mock_error.c_str();
    }

    unsigned int mysql_stmt_errno(MYSQL_STMT*) {
        return mock_errno;
    }

    unsigned long mysql_stmt_param_count(MYSQL_STMT* stmt) {
        return stmt->param_count;
    }

    MYSQL_RES* mysql_stmt_result_metadata(MYSQL_STMT*) {
        if (mock_field_count > 0) {
            mock_result.field_count = mock_field_count;
            mock_result.fields = mock_fields;
            return &mock_result;
        }
        return nullptr;
    }

    int mysql_stmt_store_result(MYSQL_STMT*) {
        return 0;
    }

    my_ulonglong mysql_stmt_num_rows(MYSQL_STMT*) {
        return mock_data.size();
    }

    int mysql_stmt_bind_param(MYSQL_STMT*, MYSQL_BIND*) {
        return 0;
    }

    int mysql_stmt_bind_result(MYSQL_STMT*, MYSQL_BIND*) {
        return 0;
    }

    int mysql_stmt_fetch(MYSQL_STMT*) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return MYSQL_NO_DATA;
        }
        return 0;
    }

    my_ulonglong mysql_stmt_affected_rows(MYSQL_STMT*) {
        return mock_data.size();
    }

    int mysql_stmt_reset(MYSQL_STMT*) {
        return 0;
    }

    void mysql_free_result(MYSQL_RES*) {}

    unsigned int mysql_num_fields(MYSQL_RES* res) {
        return res->field_count;
    }

    MYSQL_FIELD* mysql_fetch_fields(MYSQL_RES* res) {
        return res->fields;
    }

    MYSQL_RES* mysql_store_result(MYSQL*) {
        if (mock_field_count > 0) {
            mock_result.field_count = mock_field_count;
            mock_result.row_count = mock_data.size();
            return &mock_result;
        }
        return nullptr;
    }

    MYSQL_ROW mysql_fetch_row(MYSQL_RES*) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return nullptr;
        }
        static std::vector<char*> row_ptrs;
        row_ptrs.clear();
        for (const auto& val : mock_data[mock_current_row]) {
            row_ptrs.push_back(const_cast<char*>(val.c_str()));
        }
        return row_ptrs.data();
    }

    int mysql_options(MYSQL*, enum mysql_option, const void*) {
        return 0;
    }

    int SQLGetData(SQLHSTMT, SQLUSMALLINT, SQLSMALLINT, SQLPOINTER, SQLLEN, SQLLEN*) {
        return SQL_SUCCESS;
    }
}

// Test fixture for MySQL tests
class MySQLTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_errno = 0;
        mock_error = "";
        mock_connection_status = 1;
        mock_fields = nullptr;
        mock_field_count = 0;
        mock_data.clear();
        mock_current_row = -1;
    }

    void TearDown() override {
        // Cleanup is handled by MockFieldsGuard
        if (mock_fields) {
            delete[] mock_fields;
            mock_fields = nullptr;
        }
        mock_field_count = 0;
    }

    std::unique_ptr<MockFieldsGuard> setupMockFields(
        const std::vector<std::pair<std::string, enum_field_types>>& fields) {
        mock_field_count = fields.size();
        auto* fields_array = new MYSQL_FIELD[mock_field_count];
        for (size_t i = 0; i < fields.size(); ++i) {
            fields_array[i].name = const_cast<char*>(fields[i].first.c_str());
            fields_array[i].type = fields[i].second;
            fields_array[i].length = 255;
            fields_array[i].flags = 0;
            fields_array[i].decimals = 0;
        }
        return std::make_unique<MockFieldsGuard>(fields_array, mock_field_count);
    }
};

// ============================================================================
// MySQLTest Tests
// ============================================================================

// Tests auto reconnect functionality
TEST_F(MySQLTest, AutoReconnect) {
    MySQLConnection conn;
    EXPECT_NO_THROW(conn.connect(config_));
    
    // Test auto-reconnect behavior
    EXPECT_TRUE(conn.is_connected());
    
    // Simulate connection loss and auto-reconnect
    EXPECT_NO_THROW(conn.ping());
}

// Tests connect failure handling
TEST_F(MySQLTest, ConnectFailure) {
    mock_connection_status = 0;
    mock_errno = 2003;
    mock_error = "Can't connect to MySQL server";

    MySQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "invalid_host";
    params.database = "test_db";

    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests connect success
TEST_F(MySQLTest, ConnectSuccess) {
    MySQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 3306;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";

    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
    EXPECT_EQ("MySQL", conn.get_database_type());
}

// Tests connect with SSL
TEST_F(MySQLTest, ConnectWithSSL) {
    MySQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 3306;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";
    params.options["ssl_mode"] = "REQUIRED";
    params.options["ssl_ca"] = "/path/to/ca.pem";

    ASSERT_NO_THROW(conn.connect(params));
}

// Tests connection ping
TEST_F(MySQLTest, ConnectionPing) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Connection is good
    EXPECT_TRUE(conn.is_connected());

    // Simulate connection loss
    mock_connection_status = 0;
    EXPECT_FALSE(conn.is_connected());
}

// Tests connection pool integration
TEST_F(MySQLTest, ConnectionPoolIntegration) {
    // Factory for creating MySQL connections
    auto mysql_factory = []() -> std::unique_ptr<IDatabaseConnection> {
        auto conn = std::make_unique<MySQLConnection>();
        IDatabaseConnection::ConnectionParams params;
        params.host = "localhost";
        params.database = "test_db";
        params.username = "test";
        params.password = "test";
        
        // Don't connect here - let pool manage connection lifecycle
        return conn;
    };
    
    ConnectionPool pool(2, 5, mysql_factory);
    
    // Test acquiring multiple connections
    std::vector<std::unique_ptr<IDatabaseConnection>> connections;
    
    for (int i = 0; i < 3; ++i) {
        try {
            auto conn = pool.acquire(1000);
            if (conn) {
                // Connect if not already connected
                if (!conn->is_connected()) {
                    IDatabaseConnection::ConnectionParams params;
                    params.host = "localhost";
                    params.database = "test_db";
                    conn->connect(params);
                }
                connections.push_back(std::move(conn));
            }
        } catch (const std::exception& e) {
            // Expected if pool is exhausted
        }
    }
    
    EXPECT_GE(connections.size(), 2);  // At least min connections
    
    // Return connections to pool
    for (auto& conn : connections) {
        pool.release(std::move(conn));
    }
    
    auto stats = pool.get_statistics();
    EXPECT_EQ(0, stats.active_connections);
}

// Tests connection resilience
TEST_F(MySQLTest, ConnectionResilience) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    params.options["connect_timeout"] = "1";
    params.options["read_timeout"] = "5";
    params.options["write_timeout"] = "5";
    
    conn.connect(params);
    
    // Simulate connection becoming stale
    mock_connection_status = 0;  // Disconnected
    
    // Should detect disconnection
    EXPECT_FALSE(conn.is_connected());
    
    // Attempt to use connection should fail gracefully
    EXPECT_THROW(conn.execute_query("SELECT 1"), DatabaseException);
    
    // Reconnect
    mock_connection_status = 1;
    conn.disconnect();
    conn.connect(params);
    EXPECT_TRUE(conn.is_connected());
}

// Tests data type edge cases
TEST_F(MySQLTest, DataTypeEdgeCases) {
    auto fields_guard = setupMockFields({
        {"tiny_int", MYSQL_TYPE_TINY},
        {"medium_int", MYSQL_TYPE_INT24},
        {"year_col", MYSQL_TYPE_YEAR},
        {"decimal_col", MYSQL_TYPE_DECIMAL},
        {"enum_col", MYSQL_TYPE_VARCHAR},  // ENUMs appear as VARCHAR
        {"set_col", MYSQL_TYPE_VARCHAR},   // SETs appear as VARCHAR
        {"json_col", MYSQL_TYPE_VARCHAR}   // JSON appears as VARCHAR
    });
    
    mock_data = {{
        "127",          // TINYINT max
        "8388607",      // MEDIUMINT max
        "2024",         // YEAR
        "123.45",       // DECIMAL
        "option1",      // ENUM value
        "opt1,opt2",    // SET value
        R"({"key": "value"})"  // JSON
    }};
    
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Would test actual result processing in real implementation
    EXPECT_TRUE(conn.is_connected());
}

// Tests error handling
TEST_F(MySQLTest, ErrorHandling) {
    MySQLConnection conn;

    // Test disconnected query execution
    EXPECT_THROW(conn.execute_query("SELECT 1"), DatabaseException);

    // Test invalid prepared statement
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    mock_errno = 1064;
    mock_error = "You have an error in your SQL syntax";

    EXPECT_THROW(conn.prepare_statement("INVALID SQL"), DatabaseException);
}

// Tests execute query
TEST_F(MySQLTest, ExecuteQuery) {
    auto fields_guard = setupMockFields({
        {"id", MYSQL_TYPE_LONG},
        {"name", MYSQL_TYPE_VARCHAR},
        {"value", MYSQL_TYPE_DOUBLE}
    });
    
    mock_data.clear();
    mock_data = {
        {"1", "John", "100.5"},
        {"2", "Jane", "200.75"}
    };

    // Can't easily mock the complex MySQL result fetching, so we'll test the statement approach
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // The actual implementation uses prepared statements internally
    auto result = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result);
}

// Tests get version
TEST_F(MySQLTest, GetVersion) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    EXPECT_EQ("8.0.32", conn.get_version());
}

// Tests MySQL extractor
TEST_F(MySQLTest, MySQLExtractor) {
    auto conn = std::make_unique<MySQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);

    MySQLExtractor extractor(std::move(conn));
    EXPECT_EQ("mysql", extractor.get_type());
}

// Tests MySQL specific query builder
TEST_F(MySQLTest, MySQLSpecificQueryBuilder) {
    auto conn = std::make_unique<MySQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);

    MySQLExtractor extractor(std::move(conn));

    // The extractor should add MySQL-specific optimizations
    // like SQL_BUFFER_RESULT for large result sets
}

// Tests null parameter binding
TEST_F(MySQLTest, NullParameterBinding) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?)");

    // Bind NULL value
    stmt->bind(1, std::any{});
}

// Tests parameter binding types
TEST_F(MySQLTest, ParameterBindingTypes) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?, ?, ?, ?, ?, ?)");

    // Test various parameter types
    stmt->bind(1, true);                    // Boolean
    stmt->bind(2, 42);                      // Integer
    stmt->bind(3, 123456789LL);            // Long long
    stmt->bind(4, 3.14159);                // Double
    stmt->bind(5, std::string("test"));    // String
    stmt->bind(6, std::chrono::system_clock::now()); // Timestamp
}

// Tests prepared statement
TEST_F(MySQLTest, PreparedStatement) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = ?");
    ASSERT_NE(nullptr, stmt);

    // Bind parameter
    stmt->bind(1, 12345LL);

    // Clear and rebind
    stmt->clear_parameters();
    stmt->bind(1, 54321LL);
}

// Tests prepared statement edge cases
TEST_F(MySQLTest, PreparedStatementEdgeCases) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Test with maximum number of parameters
    std::string query = "INSERT INTO test VALUES (";
    for (int i = 0; i < 65535; ++i) {  // MySQL max params
        if (i > 0) query += ", ";
        query += "?";
    }
    query += ")";
    
    // This would fail in real MySQL due to packet size limits
    // but tests our handling of large parameter counts
    EXPECT_THROW({
        auto stmt = conn.prepare_statement(query);
    }, DatabaseException);
    
    // Test with empty parameter binding
    auto stmt = conn.prepare_statement("SELECT * FROM test WHERE 1=1");
    EXPECT_NO_THROW(stmt->execute_query());
    
    // Test rebinding parameters multiple times
    stmt = conn.prepare_statement("SELECT * FROM test WHERE id = ?");
    for (int i = 1; i <= 10; ++i) {
        stmt->clear_parameters();
        stmt->bind(1, i);
        // Would execute in real scenario
    }
}

// Tests prepared statement multiple params
TEST_F(MySQLTest, PreparedStatementMultipleParams) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement(
        "INSERT INTO person (person_id, gender_concept_id, birth_datetime) VALUES (?, ?, ?)");

    // Bind different types
    stmt->bind(1, 99999LL);
    stmt->bind(2, 8507);
    stmt->bind(3, std::chrono::system_clock::now());

    // Execute
    mock_data.push_back({"1"}); // One row affected
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests query timeout
TEST_F(MySQLTest, QueryTimeout) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Set timeout
    ASSERT_NO_THROW(conn.set_query_timeout(30));
}

// Tests result set data types
TEST_F(MySQLTest, ResultSetDataTypes) {
    // This test demonstrates the expected behavior even though
    // we can't fully mock the complex MySQL result handling
    setupMockFields({
        {"int_col", MYSQL_TYPE_LONG},
        {"bigint_col", MYSQL_TYPE_LONGLONG},
        {"float_col", MYSQL_TYPE_FLOAT},
        {"double_col", MYSQL_TYPE_DOUBLE},
        {"string_col", MYSQL_TYPE_VARCHAR},
        {"date_col", MYSQL_TYPE_DATE},
        {"datetime_col", MYSQL_TYPE_DATETIME},
        {"bit_col", MYSQL_TYPE_BIT}
    });

    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // The implementation would handle type conversions internally
}

// Tests result set memory management
TEST_F(MySQLTest, ResultSetMemoryManagement) {
    setupMockFields({{"id", MYSQL_TYPE_LONG}});

    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    {
        auto result = conn.execute_query("SELECT id FROM test");
        // Result should be properly cleaned up when going out of scope
    }

    // Connection should still be valid
    EXPECT_TRUE(conn.is_connected());
}

// Tests table exists
TEST_F(MySQLTest, TableExists) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Mock positive result
    mock_data = {{"1"}};
    mock_current_row = -1;
    EXPECT_TRUE(conn.table_exists("person"));

    // Mock negative result
    mock_data = {{"0"}};
    mock_current_row = -1;
    EXPECT_FALSE(conn.table_exists("nonexistent_table"));
}

// Tests transactions
TEST_F(MySQLTest, Transactions) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());

    // Cannot begin another transaction
    EXPECT_THROW(conn.begin_transaction(), DatabaseException);

    // Commit
    ASSERT_NO_THROW(conn.commit());

    // Begin another transaction
    conn.begin_transaction();

    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

#endif

File tests/unit/extract/platform_utils_test.cpp:

/**
 * @file platform_utils_test.cpp
 * @brief Unit tests for platform-specific utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <filesystem>
#include <fstream>
#include <thread>

#ifdef _WIN32
#include "extract/platform/windows_utils.h"
#else
#include "extract/platform/unix_utils.h"
#include <sys/mman.h>  // For MADV_* constants
#endif

using namespace omop::extract::platform;
namespace fs = std::filesystem;

// Test fixture for platform utilities
class PlatformUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for tests
        test_dir_ = fs::temp_directory_path() / "omop_platform_test";
        fs::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test directory
        fs::remove_all(test_dir_);
    }

    // Helper to create test file
    std::string createTestFile(const std::string& name, const std::string& content) {
        fs::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    fs::path test_dir_;
};

// ============================================================================
// Common Platform Tests
// ============================================================================

// Tests common file operations
TEST_F(PlatformUtilsTest, CommonFileOperations) {
    std::string test_file = createTestFile("common_test.txt", "test content");
    
    // Test file existence
    EXPECT_TRUE(fs::exists(test_file));
    
    // Test file size
    size_t size = get_file_size(test_file);
    EXPECT_EQ(12, size); // "test content" length
}

// Tests cross platform memory info
TEST_F(PlatformUtilsTest, CrossPlatformMemoryInfo) {
    auto mem_info = get_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    
    // Available should be less than or equal to total
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
}

// Tests cross platform timer accuracy
TEST_F(PlatformUtilsTest, CrossPlatformTimerAccuracy) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Should be at least 100ms (allowing for some overhead)
    EXPECT_GE(elapsed.count(), 95); // 95ms minimum
    // Should not be too much more than 100ms
    EXPECT_LE(elapsed.count(), 150); // 150ms maximum
}

// Tests file size retrieval
TEST_F(PlatformUtilsTest, GetFileSize) {
    std::string content = "This is a test file with known size.";
    std::string test_file = createTestFile("size_test.txt", content);

    size_t size = get_file_size(test_file);
    EXPECT_EQ(content.length(), size);

    // Test non-existent file
    EXPECT_THROW(get_file_size("nonexistent.txt"), std::exception);
}

// Tests get file modification time
TEST_F(PlatformUtilsTest, GetFileMTime) {
    std::string test_file = createTestFile("mtime_test.txt", "test");
    
    auto mtime = get_file_mtime(test_file);
    EXPECT_GT(mtime, 0);
    
    // Should be recent (within last hour)
    auto now = std::chrono::system_clock::now();
    auto now_seconds = std::chrono::duration_cast<std::chrono::seconds>(
        now.time_since_epoch()).count();
    EXPECT_LE(now_seconds - mtime, 3600); // Within last hour
}

// Tests memory mapped file
TEST_F(PlatformUtilsTest, MemoryMappedFile) {
    std::string content = "test content for memory mapping";
    std::string test_file = createTestFile("mmap_test.txt", content);
    
    // Use the correct MemoryMappedFile interface
    MemoryMappedFile mmf1;
    EXPECT_TRUE(mmf1.map_file(test_file, true)); // Read-only mode
    EXPECT_TRUE(mmf1.is_mapped());
    EXPECT_EQ(content.length(), mmf1.size()); // Actual content length
    
    // Test move semantics
    MemoryMappedFile mmf2(std::move(mmf1));
    EXPECT_FALSE(mmf1.is_mapped());
    EXPECT_TRUE(mmf2.is_mapped());
    EXPECT_EQ(content.length(), mmf2.size());
}

// Tests symbolic links
TEST_F(PlatformUtilsTest, SymbolicLinks) {
    std::string target_file = createTestFile("target.txt", "target content");
    std::string link_path = (test_dir_ / "link.txt").string();
    
    // Create symbolic link using system call since create_symbolic_link doesn't exist
    EXPECT_EQ(symlink(target_file.c_str(), link_path.c_str()), 0);
    
    // Test link exists
    EXPECT_TRUE(fs::exists(link_path));
    EXPECT_TRUE(is_symbolic_link(link_path));
    
    // Test reading through link using std::ifstream since read_file_content doesn't exist
    std::ifstream link_file(link_path);
    std::string link_content((std::istreambuf_iterator<char>(link_file)),
                            std::istreambuf_iterator<char>());
    EXPECT_EQ("target content", link_content);
}

// Tests get real path
TEST_F(PlatformUtilsTest, GetRealPath) {
    std::string test_file = createTestFile("realpath_test.txt", "test");
    
    auto real_path = get_real_path(test_file);
    EXPECT_FALSE(real_path.empty());
    EXPECT_TRUE(fs::exists(real_path));
    
    // Should be absolute path
    EXPECT_TRUE(fs::path(real_path).is_absolute());
}

#ifdef _WIN32
// ============================================================================
// Windows-Specific Tests
// ============================================================================

// Tests available drives enumeration
TEST(WindowsUtilsTest, AvailableDrives) {
    std::vector<char> drives = get_available_drives();

    // Should have at least C: drive on Windows
    EXPECT_FALSE(drives.empty());
    bool has_c_drive = std::find(drives.begin(), drives.end(), 'C') != drives.end();
    EXPECT_TRUE(has_c_drive);
}

// Tests create temp file
TEST(WindowsUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));

    // Clean up
    fs::remove(temp_file);

    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);

    // Clean up
    fs::remove(temp_file);
}

// Tests error message retrieval
TEST(WindowsUtilsTest, ErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_windows_error_message(ERROR_FILE_NOT_FOUND);
    EXPECT_FALSE(error_msg.empty());
    EXPECT_NE(error_msg.find("not found"), std::string::npos);

    // Test with current last error (0 means use GetLastError())
    SetLastError(ERROR_ACCESS_DENIED);
    error_msg = get_windows_error_message(0);
    EXPECT_FALSE(error_msg.empty());
}

// Tests file attributes
TEST_F(PlatformUtilsTest, FileAttributes) {
    std::string test_file = createTestFile("attr_test.txt", "test");

    // Get current attributes
    DWORD attrs = get_file_attributes(test_file);
    EXPECT_NE(INVALID_FILE_ATTRIBUTES, attrs);

    // Set hidden attribute
    EXPECT_TRUE(set_file_attributes(test_file, attrs | FILE_ATTRIBUTE_HIDDEN));

    // Verify attribute was set
    DWORD new_attrs = get_file_attributes(test_file);
    EXPECT_TRUE(new_attrs & FILE_ATTRIBUTE_HIDDEN);
}

// Tests file handle wrapper
TEST_F(PlatformUtilsTest, WindowsFileHandle) {
    std::string test_file = createTestFile("test.txt", "test content");
    std::wstring wide_path = utf8_to_wide(test_file);

    {
        WindowsFileHandle handle(CreateFileW(
            wide_path.c_str(),
            GENERIC_READ,
            FILE_SHARE_READ,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr));

        EXPECT_TRUE(handle.is_valid());
        EXPECT_NE(INVALID_HANDLE_VALUE, handle.get());
    }
    // Handle should be closed after scope

    // Test move semantics
    WindowsFileHandle handle1(CreateFileW(
        wide_path.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));

    WindowsFileHandle handle2(std::move(handle1));
    EXPECT_FALSE(handle1.is_valid());
    EXPECT_TRUE(handle2.is_valid());
}

// Tests file mapping
TEST_F(PlatformUtilsTest, FileMapping) {
    std::string test_file = createTestFile("mapping_test.txt", "test content for file mapping");
    
    WindowsFileHandle file_handle(CreateFileW(
        utf8_to_wide(test_file).c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));
    
    EXPECT_TRUE(file_handle.is_valid());
    
    // Test file mapping
    WindowsFileMapping mapping(file_handle.get(), PAGE_READONLY, 0, 0);
    EXPECT_TRUE(mapping.is_valid());
    EXPECT_EQ(35, mapping.size()); // "test content for file mapping" length
}

// Tests high resolution timer
TEST(WindowsUtilsTest, HighResTimer) {
    WindowsHighResTimer timer;

    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto elapsed = timer.elapsed();
    EXPECT_GE(elapsed, 0.095); // At least 95ms
    EXPECT_LE(elapsed, 0.150); // No more than 150ms
}

// Tests memory info
TEST(WindowsUtilsTest, MemoryInfo) {
    auto mem_info = get_windows_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_GT(mem_info.total_virtual, 0);
    EXPECT_GT(mem_info.available_virtual, 0);
    
    // Available should be less than or equal to total
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
    EXPECT_LE(mem_info.available_virtual, mem_info.total_virtual);
}

// Tests network path detection
TEST(WindowsUtilsTest, NetworkPath) {
    // UNC paths
    EXPECT_TRUE(is_network_path("\\\\server\\share\\file.txt"));
    EXPECT_TRUE(is_network_path("\\\\192.168.1.1\\folder"));

    // Local paths
    EXPECT_FALSE(is_network_path("C:\\Windows\\System32"));
    EXPECT_FALSE(is_network_path("D:\\Data\\file.txt"));

    // Relative paths
    EXPECT_FALSE(is_network_path("relative\\path.txt"));
}

// Tests process priority
TEST(WindowsUtilsTest, ProcessPriority) {
    // Test getting current priority
    int priority = get_process_priority();
    EXPECT_GE(priority, 0);
    EXPECT_LE(priority, 5);
    
    // Test setting priority
    EXPECT_TRUE(set_process_priority(PROCESS_PRIORITY_CLASS_NORMAL));
}

// Tests string conversion utilities
TEST(WindowsUtilsTest, StringConversion) {
    // Test UTF-8 to wide string conversion
    std::string utf8_str = "Test String éàü";
    std::wstring wide_str = utf8_to_wide(utf8_str);
    EXPECT_FALSE(wide_str.empty());

    // Test wide to UTF-8 conversion
    std::string converted_back = wide_to_utf8(wide_str);
    EXPECT_EQ(utf8_str, converted_back);

    // Test empty string
    EXPECT_TRUE(utf8_to_wide("").empty());
    EXPECT_TRUE(wide_to_utf8(L"").empty());
}

// Tests temporary directory
TEST(WindowsUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
}

#else
// ============================================================================
// Unix-Specific Tests
// ============================================================================

// Tests CPU count
TEST(UnixUtilsTest, CpuCount) {
    int cpu_count = get_cpu_count();
    EXPECT_GT(cpu_count, 0);
    EXPECT_LE(cpu_count, 1024); // Reasonable upper limit
}

// Tests Unix create temp file
TEST(UnixUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));

    // Clean up
    fs::remove(temp_file);

    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);

    // Clean up
    fs::remove(temp_file);
}

// Tests Unix file permissions
TEST_F(PlatformUtilsTest, FilePermissions) {
    std::string test_file = createTestFile("perm_test.txt", "test");
    
    // Set permissions
    EXPECT_TRUE(set_file_permissions(test_file, 0644));
    
    // Get permissions
    mode_t perms = get_file_permissions(test_file);
    EXPECT_EQ(perms & 0777, 0644);
}

// Tests Unix high resolution timer
TEST(UnixUtilsTest, HighResTimer) {
    UnixHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Use the correct method name
    auto elapsed = timer.elapsed_seconds();
    
    // Should be at least 0.1 seconds
    EXPECT_GE(elapsed, 0.095);
    
    // Reset and test again
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    auto reset_elapsed = timer.elapsed_seconds();
    EXPECT_GE(reset_elapsed, 0.045);
}

// Tests memory advice
TEST(UnixUtilsTest, MemoryAdvice) {
    void* ptr = malloc(4096);
    ASSERT_NE(ptr, nullptr);
    
    // Use the correct function name
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_NORMAL));
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_RANDOM));
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_SEQUENTIAL));
    
    free(ptr);
}

// Tests memory info
TEST(UnixUtilsTest, MemoryInfo) {
    // Use the correct function name
    auto mem_info = get_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
}

// Tests memory locking
TEST(UnixUtilsTest, MemoryLocking) {
    void* ptr = malloc(4096);
    ASSERT_NE(ptr, nullptr);
    
    // Test memory locking
    EXPECT_TRUE(lock_memory(ptr, 4096));
    EXPECT_TRUE(unlock_memory(ptr, 4096));
    
    free(ptr);
}

// Tests mounted filesystems
TEST(UnixUtilsTest, MountedFilesystems) {
    auto filesystems = get_mounted_filesystems();
    
    // Should have at least root filesystem
    EXPECT_FALSE(filesystems.empty());
    
    // Check for root filesystem
    bool has_root = false;
    for (const auto& fs : filesystems) {
        if (fs == "/") {
            has_root = true;
            break;
        }
    }
    EXPECT_TRUE(has_root);
}

// Tests network path detection
TEST(UnixUtilsTest, NetworkPath) {
    // Most Unix systems may not detect NFS paths as network paths without actual mounts
    // Test with known local paths instead
    EXPECT_FALSE(is_network_path("/home/<USER>/file.txt"));
    EXPECT_FALSE(is_network_path("/tmp/test.txt"));
    
    // Relative paths
    EXPECT_FALSE(is_network_path("relative/path.txt"));
}

// Tests process priority
TEST(UnixUtilsTest, ProcessPriority) {
    // Test getting current priority
    int priority = get_process_priority();
    EXPECT_GE(priority, -20); // Nice range
    EXPECT_LE(priority, 19);
    
    // Test setting priority
    EXPECT_TRUE(set_process_priority(0)); // Normal priority
}

// Tests system error message
TEST(UnixUtilsTest, SystemErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_system_error_message(ENOENT);
    EXPECT_FALSE(error_msg.empty());
    // Error messages may vary, just check it's not empty
    
    // Test with current errno
    errno = EACCES;
    error_msg = get_system_error_message(0);
    EXPECT_FALSE(error_msg.empty());
}

// Tests temporary directory
TEST(UnixUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
    
    // Should be writable
    std::string test_file = temp_dir + "/test_write.txt";
    std::ofstream file(test_file);
    EXPECT_TRUE(file.is_open());
    file.close();
    fs::remove(test_file);
}

// Tests Unix file descriptor
TEST_F(PlatformUtilsTest, UnixFileDescriptor) {
    std::string test_file = createTestFile("fd_test.txt", "test content");
    
    UnixFileDescriptor fd1(open(test_file.c_str(), O_RDONLY));
    EXPECT_TRUE(fd1.is_valid());
    EXPECT_GE(fd1.get(), 0);
    
    // Test move semantics
    UnixFileDescriptor fd2(std::move(fd1));
    EXPECT_FALSE(fd1.is_valid());
    EXPECT_TRUE(fd2.is_valid());
}

#endif // _WIN32

File tests/unit/extract/odbc_connector_test.cpp:

/**
 * @file odbc_connector_test.cpp
 * @brief Unit tests for ODBC connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifdef OMOP_HAS_ODBC

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/odbc_connector.h"
#include "common/exceptions.h"
#include <cstring>
#include <vector>

using namespace omop::extract;
using namespace omop::common;

// Mock ODBC structures and data
static SQLRETURN mock_sql_return = SQL_SUCCESS;
static SQLINTEGER mock_row_count = 0;
static SQLSMALLINT mock_column_count = 0;
static std::vector<std::string> mock_column_names;
static std::vector<SQLSMALLINT> mock_column_types;
static std::vector<std::vector<std::string>> mock_data;
static int mock_current_row = -1;
static std::string mock_error_message = "";
static SQLCHAR mock_sql_state[6] = "00000";
static SQLINTEGER mock_native_error = 0;
static SQLHDBC mock_connection = reinterpret_cast<SQLHDBC>(0x1000);
static SQLHSTMT mock_statement = reinterpret_cast<SQLHSTMT>(0x2000);
static SQLHENV mock_environment = reinterpret_cast<SQLHENV>(0x3000);
static SQLINTEGER mock_connection_dead = SQL_CD_FALSE;

// RAII helper for mock data
class MockDataGuard {
public:
    MockDataGuard() = default;
    
    ~MockDataGuard() {
        mock_column_names.clear();
        mock_column_types.clear();
        mock_data.clear();
        mock_current_row = -1;
        mock_column_count = 0;
    }
    
    void setColumns(const std::vector<std::string>& names,
                   const std::vector<SQLSMALLINT>& types) {
        mock_column_names = names;
        mock_column_types = types;
        mock_column_count = names.size();
    }
    
    void setData(const std::vector<std::vector<std::string>>& data) {
        mock_data = data;
    }
};

// Mock ODBC API functions
extern "C" {
    SQLRETURN SQLAllocHandle(SQLSMALLINT HandleType, SQLHANDLE InputHandle, SQLHANDLE* OutputHandle) {
        switch (HandleType) {
            case SQL_HANDLE_ENV:
                *OutputHandle = mock_environment;
                break;
            case SQL_HANDLE_DBC:
                *OutputHandle = mock_connection;
                break;
            case SQL_HANDLE_STMT:
                *OutputHandle = mock_statement;
                break;
        }
        return mock_sql_return;
    }

    SQLRETURN SQLFreeHandle(SQLSMALLINT HandleType, SQLHANDLE Handle) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLSetEnvAttr(SQLHENV EnvironmentHandle, SQLINTEGER Attribute,
                           SQLPOINTER Value, SQLINTEGER StringLength) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLDriverConnect(SQLHDBC ConnectionHandle, SQLHWND WindowHandle,
                               SQLCHAR* InConnectionString, SQLSMALLINT StringLength1,
                               SQLCHAR* OutConnectionString, SQLSMALLINT BufferLength,
                               SQLSMALLINT* StringLength2Ptr, SQLUSMALLINT DriverCompletion) {
        if (OutConnectionString && BufferLength > 0) {
            strncpy(reinterpret_cast<char*>(OutConnectionString),
                    reinterpret_cast<char*>(InConnectionString), BufferLength - 1);
        }
        if (StringLength2Ptr) {
            *StringLength2Ptr = strlen(reinterpret_cast<char*>(InConnectionString));
        }
        return mock_sql_return;
    }

    SQLRETURN SQLDisconnect(SQLHDBC ConnectionHandle) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLGetConnectAttr(SQLHDBC ConnectionHandle, SQLINTEGER Attribute,
                               SQLPOINTER Value, SQLINTEGER BufferLength,
                               SQLINTEGER* StringLengthPtr) {
        if (Attribute == SQL_ATTR_CONNECTION_DEAD) {
            *static_cast<SQLINTEGER*>(Value) = mock_connection_dead;
        }
        return SQL_SUCCESS;
    }

    SQLRETURN SQLSetConnectAttr(SQLHDBC ConnectionHandle, SQLINTEGER Attribute,
                               SQLPOINTER Value, SQLINTEGER StringLength) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLExecDirect(SQLHSTMT StatementHandle, SQLCHAR* StatementText,
                           SQLINTEGER TextLength) {
        mock_current_row = -1;
        return mock_sql_return;
    }

    SQLRETURN SQLPrepare(SQLHSTMT StatementHandle, SQLCHAR* StatementText,
                        SQLINTEGER TextLength) {
        return mock_sql_return;
    }

    SQLRETURN SQLExecute(SQLHSTMT StatementHandle) {
        mock_current_row = -1;
        return mock_sql_return;
    }

    SQLRETURN SQLNumResultCols(SQLHSTMT StatementHandle, SQLSMALLINT* ColumnCountPtr) {
        *ColumnCountPtr = mock_column_count;
        return SQL_SUCCESS;
    }

    SQLRETURN SQLDescribeCol(SQLHSTMT StatementHandle, SQLUSMALLINT ColumnNumber,
                            SQLCHAR* ColumnName, SQLSMALLINT BufferLength,
                            SQLSMALLINT* NameLengthPtr, SQLSMALLINT* DataTypePtr,
                            SQLULEN* ColumnSizePtr, SQLSMALLINT* DecimalDigitsPtr,
                            SQLSMALLINT* NullablePtr) {
        if (ColumnNumber > 0 && ColumnNumber <= mock_column_count) {
            if (ColumnName && BufferLength > 0) {
                strncpy(reinterpret_cast<char*>(ColumnName),
                       mock_column_names[ColumnNumber - 1].c_str(), BufferLength - 1);
            }
            if (NameLengthPtr) {
                *NameLengthPtr = mock_column_names[ColumnNumber - 1].length();
            }
            if (DataTypePtr) {
                *DataTypePtr = mock_column_types[ColumnNumber - 1];
            }
            if (ColumnSizePtr) {
                *ColumnSizePtr = 255;
            }
            if (DecimalDigitsPtr) {
                *DecimalDigitsPtr = 0;
            }
            if (NullablePtr) {
                *NullablePtr = SQL_NULLABLE;
            }
            return SQL_SUCCESS;
        }
        return SQL_ERROR;
    }

    SQLRETURN SQLFetch(SQLHSTMT StatementHandle) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return SQL_NO_DATA;
        }
        return SQL_SUCCESS;
    }

    SQLRETURN SQLGetData(SQLHSTMT StatementHandle, SQLUSMALLINT ColumnNumber,
                        SQLSMALLINT TargetType, SQLPOINTER TargetValue,
                        SQLLEN BufferLength, SQLLEN* StrLen_or_IndPtr) {
        if (ColumnNumber > 0 && ColumnNumber <= mock_column_count &&
            mock_current_row >= 0 && mock_current_row < static_cast<int>(mock_data.size())) {

            const std::string& value = mock_data[mock_current_row][ColumnNumber - 1];

            if (value == "NULL") {
                if (StrLen_or_IndPtr) {
                    *StrLen_or_IndPtr = SQL_NULL_DATA;
                }
            } else {
                switch (TargetType) {
                    case SQL_C_SLONG:
                        *static_cast<SQLINTEGER*>(TargetValue) = std::stoi(value);
                        break;
                    case SQL_C_SBIGINT:
                        *static_cast<SQLBIGINT*>(TargetValue) = std::stoll(value);
                        break;
                    case SQL_C_DOUBLE:
                        *static_cast<SQLDOUBLE*>(TargetValue) = std::stod(value);
                        break;
                    case SQL_C_CHAR:
                        if (BufferLength > 0) {
                            strncpy(static_cast<char*>(TargetValue), value.c_str(), BufferLength - 1);
                            static_cast<char*>(TargetValue)[BufferLength - 1] = '\0';
                        }
                        break;
                    case SQL_C_TYPE_DATE:
                        // Simplified date handling
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->year = 2024;
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->month = 1;
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->day = 15;
                        break;
                    case SQL_C_TYPE_TIMESTAMP:
                        // Simplified timestamp handling
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->year = 2024;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->month = 1;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->day = 15;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->hour = 10;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->minute = 30;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->second = 45;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->fraction = 0;
                        break;
                }
                if (StrLen_or_IndPtr) {
                    *StrLen_or_IndPtr = value.length();
                }
            }
        }
        return SQL_SUCCESS;
    }

    SQLRETURN SQLRowCount(SQLHSTMT StatementHandle, SQLLEN* RowCountPtr) {
        *RowCountPtr = mock_row_count;
        return SQL_SUCCESS;
    }

    SQLRETURN SQLGetDiagRec(SQLSMALLINT HandleType, SQLHANDLE Handle,
                           SQLSMALLINT RecNumber, SQLCHAR* SQLState,
                           SQLINTEGER* NativeErrorPtr, SQLCHAR* MessageText,
                           SQLSMALLINT BufferLength, SQLSMALLINT* TextLengthPtr) {
        if (RecNumber != 1) {
            return SQL_NO_DATA;
        }

        if (SQLState) {
            memcpy(SQLState, mock_sql_state, 6);
        }
        if (NativeErrorPtr) {
            *NativeErrorPtr = mock_native_error;
        }
        if (MessageText && BufferLength > 0) {
            strncpy(reinterpret_cast<char*>(MessageText),
                   mock_error_message.c_str(), BufferLength - 1);
        }
        if (TextLengthPtr) {
            *TextLengthPtr = mock_error_message.length();
        }
        return SQL_SUCCESS;
    }

    SQLRETURN SQLBindParameter(SQLHSTMT StatementHandle, SQLUSMALLINT ParameterNumber,
                              SQLSMALLINT InputOutputType, SQLSMALLINT ValueType,
                              SQLSMALLINT ParameterType, SQLULEN ColumnSize,
                              SQLSMALLINT DecimalDigits, SQLPOINTER ParameterValuePtr,
                              SQLLEN BufferLength, SQLLEN* StrLen_or_IndPtr) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLFreeStmt(SQLHSTMT StatementHandle, SQLUSMALLINT Option) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLEndTran(SQLSMALLINT HandleType, SQLHANDLE Handle,
                        SQLSMALLINT CompletionType) {
        return SQL_SUCCESS;
    }

    SQLRETURN SQLGetInfo(SQLHDBC ConnectionHandle, SQLUSMALLINT InfoType,
                        SQLPOINTER InfoValuePtr, SQLSMALLINT BufferLength,
                        SQLSMALLINT* StringLengthPtr) {
        switch (InfoType) {
            case SQL_DRIVER_NAME:
                if (InfoValuePtr && BufferLength > 0) {
                    strncpy(static_cast<char*>(InfoValuePtr), "MockODBC", BufferLength - 1);
                }
                break;
            case SQL_DBMS_VER:
                if (InfoValuePtr && BufferLength > 0) {
                    strncpy(static_cast<char*>(InfoValuePtr), "1.0.0", BufferLength - 1);
                }
                break;
        }
        return SQL_SUCCESS;
    }

    SQLRETURN SQLTables(SQLHSTMT StatementHandle, SQLCHAR* CatalogName,
                       SQLSMALLINT NameLength1, SQLCHAR* SchemaName,
                       SQLSMALLINT NameLength2, SQLCHAR* TableName,
                       SQLSMALLINT NameLength3, SQLCHAR* TableType,
                       SQLSMALLINT NameLength4) {
        // Set up mock data for table existence check
        mock_column_count = 5; // TABLE_CAT, TABLE_SCHEM, TABLE_NAME, TABLE_TYPE, REMARKS
        mock_column_names = {"TABLE_CAT", "TABLE_SCHEM", "TABLE_NAME", "TABLE_TYPE", "REMARKS"};
        mock_current_row = -1;
        return SQL_SUCCESS;
    }

    SQLRETURN SQLDrivers(SQLHENV EnvironmentHandle, SQLUSMALLINT Direction,
                        SQLCHAR* DriverDescription, SQLSMALLINT BufferLength1,
                        SQLSMALLINT* DescriptionLengthPtr, SQLCHAR* DriverAttributes,
                        SQLSMALLINT BufferLength2, SQLSMALLINT* AttributesLengthPtr) {
        static int driver_index = 0;
        if (Direction == SQL_FETCH_FIRST) {
            driver_index = 0;
        }

        if (driver_index >= 2) {
            return SQL_NO_DATA;
        }

        if (driver_index == 0) {
            if (DriverDescription && BufferLength1 > 0) {
                strncpy(reinterpret_cast<char*>(DriverDescription), "Mock ODBC Driver", BufferLength1 - 1);
            }
            if (DriverAttributes && BufferLength2 > 0) {
                strncpy(reinterpret_cast<char*>(DriverAttributes), "Version=1.0\0", BufferLength2 - 1);
            }
        } else {
            if (DriverDescription && BufferLength1 > 0) {
                strncpy(reinterpret_cast<char*>(DriverDescription), "Test Driver", BufferLength1 - 1);
            }
        }

        driver_index++;
        return SQL_SUCCESS;
    }

    SQLRETURN SQLDataSources(SQLHENV EnvironmentHandle, SQLUSMALLINT Direction,
                            SQLCHAR* ServerName, SQLSMALLINT BufferLength1,
                            SQLSMALLINT* NameLength1Ptr, SQLCHAR* Description,
                            SQLSMALLINT BufferLength2, SQLSMALLINT* NameLength2Ptr) {
        static int dsn_index = 0;
        if (Direction == SQL_FETCH_FIRST) {
            dsn_index = 0;
        }

        if (dsn_index >= 1) {
            return SQL_NO_DATA;
        }

        if (ServerName && BufferLength1 > 0) {
            strncpy(reinterpret_cast<char*>(ServerName), "TestDSN", BufferLength1 - 1);
        }
        if (Description && BufferLength2 > 0) {
            strncpy(reinterpret_cast<char*>(Description), "Test Data Source", BufferLength2 - 1);
        }

        dsn_index++;
        return SQL_SUCCESS;
    }
}

// Test fixture for ODBC tests
class OdbcTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_sql_return = SQL_SUCCESS;
        mock_row_count = 0;
        mock_column_count = 0;
        mock_column_names.clear();
        mock_column_types.clear();
        mock_data.clear();
        mock_current_row = -1;
        mock_error_message = "";
        strcpy(reinterpret_cast<char*>(mock_sql_state), "00000");
        mock_native_error = 0;
        mock_connection_dead = SQL_CD_FALSE;
    }
    
    std::unique_ptr<MockDataGuard> data_guard_;
};

// Tests ODBC connection
TEST_F(OdbcTest, ConnectSuccess) {
    OdbcDatabaseConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "SQL Server";
    params.host = "localhost";
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";

    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
}

// Tests DSN-based connection
TEST_F(OdbcTest, ConnectWithDSN) {
    OdbcDatabaseConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.options["DSN"] = "MyDataSource";
    params.username = "test_user";
    params.password = "test_pass";

    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
}

// Tests connection failure
TEST_F(OdbcTest, DISABLED_ConnectFailure) {
    mock_sql_return = SQL_ERROR;
    mock_native_error = 1;
    mock_error_message = "Unable to connect to data source";
    strcpy(reinterpret_cast<char*>(mock_sql_state), "08001");

    OdbcDatabaseConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Invalid Driver";
    params.host = "invalid_host";

    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests query execution
TEST_F(OdbcTest, ExecuteQuery) {
    data_guard_ = std::make_unique<MockDataGuard>();
    data_guard_->setColumns(
        {"id", "name", "value"},
        {SQL_INTEGER, SQL_VARCHAR, SQL_DOUBLE}
    );
    data_guard_->setData({
        {"1", "John", "100.5"},
        {"2", "Jane", "200.75"}
    });

    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result);

    // Check metadata
    EXPECT_EQ(3, result->column_count());
    EXPECT_EQ("id", result->column_name(0));
    EXPECT_EQ("name", result->column_name(1));
    EXPECT_EQ("value", result->column_name(2));

    // Check first row
    ASSERT_TRUE(result->next());
    EXPECT_EQ(1, std::any_cast<int>(result->get_value(0)));
    EXPECT_EQ("John", std::any_cast<std::string>(result->get_value(1)));
    EXPECT_DOUBLE_EQ(100.5, std::any_cast<double>(result->get_value(2)));

    // Check second row
    ASSERT_TRUE(result->next());
    EXPECT_EQ(2, std::any_cast<int>(result->get_value(0)));

    // No more rows
    EXPECT_FALSE(result->next());
}

// Tests NULL value handling
TEST_F(OdbcTest, NullValueHandling) {
    mock_column_count = 3;
    mock_column_names = {"id", "name", "optional"};
    mock_column_types = {SQL_INTEGER, SQL_VARCHAR, SQL_INTEGER};
    mock_data = {{"1", "Test", "NULL"}};

    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());

    EXPECT_FALSE(result->is_null(0));
    EXPECT_FALSE(result->is_null(1));
    EXPECT_TRUE(result->is_null(2));

    EXPECT_FALSE(result->is_null("id"));
    EXPECT_TRUE(result->is_null("optional"));
}

// Tests prepared statements
TEST_F(OdbcTest, PreparedStatement) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = ?");
    ASSERT_NE(nullptr, stmt);

    // Bind parameter
    stmt->bind(1, 12345LL);

    // Set up result
    mock_column_count = 2;
    mock_column_names = {"person_id", "birth_date"};
    mock_column_types = {SQL_BIGINT, SQL_TYPE_DATE};
    mock_data = {{"12345", "2000-01-15"}};

    auto result = stmt->execute_query();
    ASSERT_NE(nullptr, result);
}

// Tests execute update
TEST_F(OdbcTest, ExecuteUpdate) {
    mock_row_count = 5;

    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    size_t affected = conn.execute_update("UPDATE test_table SET value = 100");
    EXPECT_EQ(5, affected);
}

// Tests transactions
TEST_F(OdbcTest, Transactions) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());

    // Commit
    ASSERT_NO_THROW(conn.commit());

    // Begin another transaction
    conn.begin_transaction();

    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

// Tests table existence
TEST_F(OdbcTest, TableExists) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    // Mock table exists
    mock_data = {{"", "", "person", "TABLE", ""}};
    EXPECT_TRUE(conn.table_exists("person"));

    // Mock table doesn't exist
    mock_data.clear();
    EXPECT_FALSE(conn.table_exists("nonexistent"));
}

// Tests version retrieval
TEST_F(OdbcTest, GetVersion) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    EXPECT_EQ("1.0.0", conn.get_version());
}

// Tests database type detection
TEST_F(OdbcTest, GetDatabaseType) {
    OdbcDatabaseConnection conn;

    // Test with a generic driver
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Generic ODBC Driver";
    conn.connect(params);
    // Just verify it returns a non-empty string
    EXPECT_FALSE(conn.get_database_type().empty());
}

// Tests error handling
TEST_F(OdbcTest, ErrorHandling) {
    OdbcDatabaseConnection conn;

    // Test query on disconnected connection
    EXPECT_THROW(conn.execute_query("SELECT 1"), DatabaseException);

    // Test connection dead detection
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    mock_connection_dead = SQL_CD_TRUE;
    EXPECT_FALSE(conn.is_connected());
}

// Tests ODBC driver manager
TEST_F(OdbcTest, DriverManager) {
    // Test getting available drivers
    auto drivers = OdbcDriverManager::get_available_drivers();
    EXPECT_GE(drivers.size(), 1);
    EXPECT_EQ("Mock ODBC Driver", drivers[0].name);

    // Test getting data sources
    auto sources = OdbcDriverManager::get_data_sources();
    EXPECT_GE(sources.size(), 1);
    EXPECT_EQ("TestDSN", sources[0].name);

    // Test connection testing
    auto [success, message] = OdbcDriverManager::test_connection("Driver={Test Driver}");
    EXPECT_TRUE(success);
}

// Tests ODBC extractor
TEST_F(OdbcTest, OdbcExtractor) {
    auto conn = std::make_unique<OdbcDatabaseConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn->connect(params);

    OdbcExtractor extractor(std::move(conn));
    EXPECT_EQ("odbc", extractor.get_type());
}

// Tests result set column types
TEST_F(OdbcTest, ResultSetColumnTypes) {
    mock_column_count = 7;
    mock_column_names = {"bit_col", "int_col", "bigint_col", "float_col",
                        "date_col", "time_col", "timestamp_col"};
    mock_column_types = {SQL_BIT, SQL_INTEGER, SQL_BIGINT, SQL_DOUBLE,
                        SQL_TYPE_DATE, SQL_TYPE_TIME, SQL_TYPE_TIMESTAMP};
    mock_data = {{"1", "42", "123456789", "3.14159", "2024-01-15", "10:30:45", "2024-01-15 10:30:45"}};

    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test");
    ASSERT_TRUE(result->next());

    // Check type names
    EXPECT_EQ("BIT", result->column_type(0));
    EXPECT_EQ("INTEGER", result->column_type(1));
    EXPECT_EQ("BIGINT", result->column_type(2));
    EXPECT_EQ("DOUBLE", result->column_type(3));
    EXPECT_EQ("DATE", result->column_type(4));
    EXPECT_EQ("TIME", result->column_type(5));
    EXPECT_EQ("TIMESTAMP", result->column_type(6));
}

// Tests parameter binding for different types
TEST_F(OdbcTest, ParameterBindingTypes) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?, ?, ?, ?, ?, ?)");

    // Test various parameter types
    stmt->bind(1, true);                    // Boolean
    stmt->bind(2, 42);                      // Integer
    stmt->bind(3, 123456789LL);            // Long long
    stmt->bind(4, 3.14159);                // Double
    stmt->bind(5, std::string("test"));    // String
    stmt->bind(6, std::chrono::system_clock::now()); // Timestamp

    mock_row_count = 1;
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests query timeout
TEST_F(OdbcTest, QueryTimeout) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);

    ASSERT_NO_THROW(conn.set_query_timeout(30));
}

#endif

File tests/unit/extract/extractor_base_test.cpp:

/**
 * @file extractor_base_test.cpp
 * @brief Unit tests for extractor base class functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_base.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/validation.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;

// Mock extractor implementation for testing ExtractorBase functionality
class MockExtractor : public ExtractorBase {
public:
    MockExtractor(const std::string& name = "test_extractor",
                  std::shared_ptr<ConfigurationManager> config = nullptr,
                  std::shared_ptr<Logger> logger = nullptr)
        : ExtractorBase(name, config, logger),
          mock_records_(),
          current_record_(0),
          should_connect_(true),
          should_validate_(true),
          mock_schema_() {

        // Set up default mock schema
        mock_schema_.source_name = "test_source";
        mock_schema_.source_type = "mock";
        SourceSchema::Column col1{"id", "integer", false, {}, {}, "Primary key"};
        SourceSchema::Column col2{"name", "string", true, 255, {}, "Name field"};
        SourceSchema::Column col3{"value", "double", true, {}, {}, "Value field"};
        mock_schema_.columns = {col1, col2, col3};
        mock_schema_.primary_keys = {"id"};
    }

    // Override abstract methods
    std::string get_type() const override { return "mock"; }

    SourceSchema getSchema() const override { return mock_schema_; }

    omop::common::ValidationResult validateSource() override {
        if (should_validate_) {
            return omop::common::ValidationResult(); // Valid by default
        } else {
            omop::common::ValidationResult result;
            result.add_error("schema", "Mock validation failed", "test");
            return result;
        }
    }

    // Mock protected methods
    bool connect() override { return should_connect_; }
    void disconnect() override { /* Mock disconnect */ }

    std::vector<omop::core::Record> extractBatchImpl(size_t batch_size) override {
        std::vector<omop::core::Record> batch;
        size_t records_to_extract = std::min(batch_size, mock_records_.size() - current_record_);

        for (size_t i = 0; i < records_to_extract; ++i) {
            if (current_record_ < mock_records_.size()) {
                batch.push_back(mock_records_[current_record_++]);
            }
        }

        return batch;
    }

    omop::core::Record convertToRecord(const std::any& source_data) override {
        // Simple conversion - assume source_data is already a Record
        if (source_data.type() == typeid(omop::core::Record)) {
            return std::any_cast<omop::core::Record>(source_data);
        }
        // Create a default record if conversion fails
        omop::core::Record record;
        record.setField("converted", true);
        return record;
    }

    // Test helper methods
    void setMockRecords(const std::vector<omop::core::Record>& records) {
        mock_records_ = records;
        current_record_ = 0;
    }

    void setShouldConnect(bool should) { should_connect_ = should; }
    void setShouldValidate(bool should) { should_validate_ = should; }

    void setMockSchema(const SourceSchema& schema) { mock_schema_ = schema; }

    // Expose protected members for testing
    bool isConnected() const { return is_connected_; }
    bool isInitialized() const { return is_initialized_; }
    size_t getCurrentPosition() const { return current_position_; }
    const ExtractionStats& getStats() const { return stats_; }
    const ExtractionOptions& getOptions() const { return options_; }

    using ExtractorBase::selectColumns;
    using ExtractorBase::handleError;
    using ExtractorBase::updateProgress;

    void initialize(const std::unordered_map<std::string, std::any>& config, omop::core::ProcessingContext& context) override {
        if (auto it = config.find("columns"); it != config.end()) {
            options_.columns = std::any_cast<std::vector<std::string>>(it->second);
        }
        if (auto it = config.find("skip_records"); it != config.end()) {
            options_.skip_records = std::any_cast<size_t>(it->second);
        }
        if (auto it = config.find("filter_expression"); it != config.end()) {
            options_.filter_expression = std::any_cast<std::string>(it->second);
        }
        ExtractorBase::initialize(config, context);
    }

protected:
    void resetImpl() override {
        current_record_ = 0;
    }

private:
    std::vector<omop::core::Record> mock_records_;
    size_t current_record_;
    bool should_connect_;
    bool should_validate_;
    SourceSchema mock_schema_;
};

// Test fixture for extractor base tests
class ExtractorBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<ConfigurationManager>();
        logger_ = std::make_shared<Logger>("test");
        extractor_ = std::make_unique<MockExtractor>("test_extractor", config_, logger_);
    }

    void TearDown() override {
        if (extractor_ && extractor_->isInitialized()) {
            extractor_->close();
        }
    }

    std::shared_ptr<ConfigurationManager> config_;
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<MockExtractor> extractor_;
};

// ============================================================================
// ExtractorBaseTest Tests
// ============================================================================

// Tests constructor functionality
TEST_F(ExtractorBaseTest, Constructor) {
    EXPECT_NO_THROW({
        MockExtractor extractor("test_name", config_, logger_);
    });

    EXPECT_EQ(extractor_->getName(), "test_extractor");
    EXPECT_EQ(extractor_->get_type(), "mock");
    EXPECT_FALSE(extractor_->isInitialized());
    EXPECT_FALSE(extractor_->isConnected());
}

// Tests custom options configuration
TEST_F(ExtractorBaseTest, CustomOptions) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(1000);
    config["max_records"] = size_t(5000);
    config["continue_on_error"] = true;
    config["validate_schema"] = false;
    config["columns"] = std::vector<std::string>{"id", "name"};
    config["skip_records"] = size_t(10);
    config["filter_expression"] = std::string("id > 100");

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 1000);
    EXPECT_EQ(options.max_records, 5000);
    EXPECT_TRUE(options.continue_on_error);
    EXPECT_FALSE(options.validate_schema);
    EXPECT_EQ(options.columns, (std::vector<std::string>{"id", "name"}));
    EXPECT_EQ(options.skip_records, 10);
    EXPECT_EQ(options.filter_expression, "id > 100");
}

// Tests current position tracking
TEST_F(ExtractorBaseTest, CurrentPositionTracking) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract in batches and verify position tracking
    EXPECT_EQ(extractor_->getCurrentPosition(), 0);

    auto batch1 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch1.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);

    auto batch2 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch2.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 6);

    auto batch3 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch3.size(), 4); // Only 4 records left
    EXPECT_EQ(extractor_->getCurrentPosition(), 10);
}

// Tests default options
TEST_F(ExtractorBaseTest, DefaultOptions) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 1000); // Default batch size
    EXPECT_EQ(options.max_records, 0); // No limit by default
    EXPECT_TRUE(options.continue_on_error); // Continue on error by default
    EXPECT_TRUE(options.validate_schema); // Validate schema by default
}

// Tests double close and finalize
TEST_F(ExtractorBaseTest, DoubleCloseAndFinalize) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // First close and finalize
    EXPECT_NO_THROW(extractor_->close());
    EXPECT_NO_THROW(extractor_->finalize(context));

    // Second close and finalize should not throw
    EXPECT_NO_THROW(extractor_->close());
    EXPECT_NO_THROW(extractor_->finalize(context));
}

// Tests extract after close
TEST_F(ExtractorBaseTest, ExtractAfterClose) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Close the extractor
    extractor_->close();

    // Try to extract after close - should throw or return empty batch
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 0);
}

// Tests extract batch with column selection
TEST_F(ExtractorBaseTest, ExtractBatchWithColumnSelection) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        record.setField("value", 100.0 + i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["columns"] = std::vector<std::string>{"id", "name"};
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);

    // Verify only selected columns are present
    for (const auto& record : batch.getRecords()) {
        EXPECT_TRUE(record.hasField("id"));
        EXPECT_TRUE(record.hasField("name"));
        EXPECT_FALSE(record.hasField("value")); // Should not be selected
    }
}

// Tests extract batch with empty records
TEST_F(ExtractorBaseTest, ExtractBatchWithEmptyRecords) {
    // Set up extractor with no records
    extractor_->setMockRecords({});

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 0);
}

// Tests extract batch with error handling
TEST_F(ExtractorBaseTest, ExtractBatchWithErrorHandling) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["continue_on_error"] = true;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);
}

// Tests extract batch with error throwing
TEST_F(ExtractorBaseTest, ExtractBatchWithErrorThrowing) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["continue_on_error"] = false;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);
}

// Tests extract batch with missing fields
TEST_F(ExtractorBaseTest, ExtractBatchWithMissingFields) {
    // Create test records with missing fields
    std::vector<Record> test_records;
    for (int i = 0; i < 3; ++i) {
        Record record;
        record.setField("id", i);
        // Some records missing 'name' field
        if (i % 2 == 0) {
            record.setField("name", "record_" + std::to_string(i));
        }
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests extract batch not initialized
TEST_F(ExtractorBaseTest, ExtractBatchNotInitialized) {
    ProcessingContext context;
    EXPECT_THROW(extractor_->extract_batch(10, context), std::runtime_error);
}

// Tests extract batch success
TEST_F(ExtractorBaseTest, ExtractBatchSuccess) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract first batch
    auto batch1 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch1.size(), 5);

    // Extract second batch
    auto batch2 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch2.size(), 5);

    // Extract third batch (should be empty)
    auto batch3 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch3.size(), 0);
}

// Tests get name returns correct value
TEST_F(ExtractorBaseTest, GetNameReturnsCorrectValue) {
    MockExtractor extractor("custom_name", config_, logger_);
    EXPECT_EQ(extractor.getName(), "custom_name");
}

// Tests get schema
TEST_F(ExtractorBaseTest, GetSchema) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto schema = extractor_->getSchema();
    EXPECT_EQ(schema.source_name, "test_source");
    EXPECT_EQ(schema.source_type, "mock");
    EXPECT_EQ(schema.columns.size(), 3);
    EXPECT_EQ(schema.primary_keys.size(), 1);
    EXPECT_EQ(schema.primary_keys[0], "id");
}

// Tests get statistics internal
TEST_F(ExtractorBaseTest, GetStatisticsInternal) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 0);
    EXPECT_EQ(stats.successful_records, 0);
    EXPECT_EQ(stats.failed_records, 0);
}

// Tests get type returns correct value
TEST_F(ExtractorBaseTest, GetTypeReturnsCorrectValue) {
    EXPECT_EQ(extractor_->get_type(), "mock");
}

// Tests handle error
TEST_F(ExtractorBaseTest, HandleError) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Test error message";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 1);
}

// Tests handle error with context
TEST_F(ExtractorBaseTest, HandleErrorWithContext) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Test error with context";
    std::any context_info = std::string("Additional context");
    EXPECT_NO_THROW(extractor_->handleError(error_message, context_info));

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 1);
}

// Tests handle multiple errors in batch
TEST_F(ExtractorBaseTest, HandleMultipleErrorsInBatch) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Handle multiple errors
    extractor_->handleError("Error 1", std::nullopt);
    extractor_->handleError("Error 2", std::nullopt);
    extractor_->handleError("Error 3", std::nullopt);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 3);
}

// Tests handle error no colon
TEST_F(ExtractorBaseTest, HandleErrorNoColon) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Error without colon";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));
}

// Tests handle error special characters
TEST_F(ExtractorBaseTest, HandleErrorSpecialCharacters) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Error with special chars: \n\t\r\"'\\";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));
}

// Tests has more data initialized
TEST_F(ExtractorBaseTest, HasMoreDataInitialized) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Should have more data initially
    EXPECT_TRUE(extractor_->has_more_data());

    // Extract all records
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);

    // Should not have more data after extraction
    EXPECT_FALSE(extractor_->has_more_data());
}

// Tests has more data not initialized
TEST_F(ExtractorBaseTest, HasMoreDataNotInitialized) {
    EXPECT_FALSE(extractor_->has_more_data());
}

// Tests initialize already initialized
TEST_F(ExtractorBaseTest, InitializeAlreadyInitialized) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // First initialization
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    EXPECT_TRUE(extractor_->isInitialized());

    // Second initialization should not throw
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    EXPECT_TRUE(extractor_->isInitialized());
}

// Tests initialize connection failure
TEST_F(ExtractorBaseTest, InitializeConnectionFailure) {
    extractor_->setShouldConnect(false);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_THROW(extractor_->initialize(config, context), std::runtime_error);
    EXPECT_FALSE(extractor_->isInitialized());
}

// Tests initialize success
TEST_F(ExtractorBaseTest, InitializeSuccess) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_NO_THROW(extractor_->initialize(config, context));

    EXPECT_TRUE(extractor_->isInitialized());
    EXPECT_TRUE(extractor_->isConnected());
}

// Tests initialize validation failure
TEST_F(ExtractorBaseTest, InitializeValidationFailure) {
    extractor_->setShouldValidate(false);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_THROW(extractor_->initialize(config, context), std::runtime_error);
    EXPECT_FALSE(extractor_->isInitialized());
}

// Tests initialize with config
TEST_F(ExtractorBaseTest, InitializeWithConfig) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(5000);
    config["max_records"] = size_t(10000);
    config["continue_on_error"] = false;
    config["validate_schema"] = true;

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 5000);
    EXPECT_EQ(options.max_records, 10000);
    EXPECT_FALSE(options.continue_on_error);
    EXPECT_TRUE(options.validate_schema);
}

// Tests initialize with invalid config types
TEST_F(ExtractorBaseTest, InitializeWithInvalidConfigTypes) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = std::string("invalid"); // Should be size_t
    config["max_records"] = std::string("invalid"); // Should be size_t

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    // Should handle invalid types gracefully
}

// Tests progress callback
TEST_F(ExtractorBaseTest, ProgressCallback) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    bool callback_called = false;
    auto progress_callback = [&](size_t current, size_t total) {
        callback_called = true;
        EXPECT_GE(current, 0);
        EXPECT_GE(total, 0);
    };

    extractor_->setProgressCallback(progress_callback);
    EXPECT_NO_THROW(extractor_->updateProgress(5, 10));
}

// Tests progress callback called
TEST_F(ExtractorBaseTest, ProgressCallbackCalled) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    bool callback_called = false;
    extractor_->setProgressCallback([&](size_t current, size_t total) {
        callback_called = true;
    });

    extractor_->updateProgress(1, 10);
    EXPECT_TRUE(callback_called);
}

// Tests reset functionality
TEST_F(ExtractorBaseTest, Reset) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract some records
    auto batch1 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch1.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);

    // Reset the extractor
    EXPECT_NO_THROW(extractor_->reset());

    // Should be able to extract again from the beginning
    auto batch2 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch2.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);
}

// Tests select columns empty selection
TEST_F(ExtractorBaseTest, SelectColumnsEmptySelection) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Create a test record
    Record test_record;
    test_record.setField("id", 1);
    test_record.setField("name", "test");
    
    auto result = extractor_->selectColumns(test_record);
    EXPECT_EQ(result.getFieldNames().size(), 2); // Should return all fields
}

// Tests select columns with selection
TEST_F(ExtractorBaseTest, SelectColumnsWithSelection) {
    std::unordered_map<std::string, std::any> config;
    config["columns"] = std::vector<std::string>{"id", "name"};
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Create a test record
    Record test_record;
    test_record.setField("id", 1);
    test_record.setField("name", "test");
    test_record.setField("value", 100.0);
    
    auto result = extractor_->selectColumns(test_record);
    EXPECT_EQ(result.getFieldNames().size(), 2); // Should only return id and name
    EXPECT_TRUE(result.hasField("id"));
    EXPECT_TRUE(result.hasField("name"));
    EXPECT_FALSE(result.hasField("value"));
}

// Tests statistics after extraction
TEST_F(ExtractorBaseTest, StatisticsAfterExtraction) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract records
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 10);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 10);
    EXPECT_EQ(stats.successful_records, 10);
}

// Tests statistics default empty
TEST_F(ExtractorBaseTest, StatisticsDefaultEmpty) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 0);
    EXPECT_EQ(stats.successful_records, 0);
    EXPECT_EQ(stats.failed_records, 0);
}

// Tests validate source failure
TEST_F(ExtractorBaseTest, ValidateSourceFailure) {
    extractor_->setShouldValidate(false);

    auto result = extractor_->validateSource();
    EXPECT_FALSE(result.is_valid());
    EXPECT_GT(result.errors().size(), 0);
}

// Tests validate source success
TEST_F(ExtractorBaseTest, ValidateSourceSuccess) {
    auto result = extractor_->validateSource();
    EXPECT_TRUE(result.is_valid());
    EXPECT_EQ(result.errors().size(), 0);
}

File tests/unit/extract/extract_performance_test.cpp:

/**
 * @file extract_performance_test.cpp
 * @brief Performance and stress tests for extract module components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>
#include <random>
#include <future>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <unistd.h>
#include <sys/types.h>
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/extract.h"

using namespace omop::extract;
using namespace omop::core;
using namespace ::testing;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<IResultSet>, execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD(std::unique_ptr<IPreparedStatement>, prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

class PerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_perf_test";
        std::filesystem::create_directories(test_dir_);
        
        // Initialize random number generator for test data
        rng_.seed(std::chrono::steady_clock::now().time_since_epoch().count());
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    // Generate large CSV file for testing
    std::string generateLargeCsv(size_t num_records, const std::string& filename) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        
        file << "id,name,email,age,salary,department,hire_date,active\n";
        
        std::uniform_int_distribution<int> age_dist(22, 65);
        std::uniform_real_distribution<double> salary_dist(30000.0, 150000.0);
        std::uniform_int_distribution<int> bool_dist(0, 1);
        
        const std::vector<std::string> departments = {
            "Engineering", "Marketing", "Sales", "HR", "Finance", "Operations"
        };
        
        for (size_t i = 1; i <= num_records; ++i) {
            file << i << ","
                 << "User" << i << ","
                 << "user" << i << "@example.com,"
                 << age_dist(rng_) << ","
                 << std::fixed << std::setprecision(2) << salary_dist(rng_) << ","
                 << departments[i % departments.size()] << ","
                 << "2020-01-01,"
                 << (bool_dist(rng_) ? "true" : "false") << "\n";
                 
            // Flush periodically to avoid memory issues
            if (i % 10000 == 0) {
                file.flush();
            }
        }
        
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
    std::mt19937 rng_;
};

// Test CSV extraction performance with large files
TEST_F(PerformanceTest, LargeCsvExtractionPerformance) {
    const size_t num_records = 100000;
    std::string filepath = generateLargeCsv(num_records, "large_test.csv");
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["batch_size"] = size_t(10000);
    
    ProcessingContext context;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    extractor.initialize(config, context);
    
    size_t total_extracted = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10000, context);
        total_extracted += batch.size();
    }
    
    extractor.finalize(context);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(total_extracted, num_records);
    
    // Performance assertion: should process at least 10,000 records per second
    double records_per_second = static_cast<double>(total_extracted) / (duration.count() / 1000.0);
    EXPECT_GE(records_per_second, 10000.0) 
        << "Performance below threshold: " << records_per_second << " records/sec";
    
    // Memory usage should be reasonable
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("extracted_count"));
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), num_records);
}

// Test concurrent CSV processing
TEST_F(PerformanceTest, ConcurrentCsvProcessing) {
    const size_t num_files = 4;
    const size_t records_per_file = 25000;
    std::vector<std::string> test_files;
    
    // Create multiple test files
    for (size_t i = 0; i < num_files; ++i) {
        std::string filename = "concurrent_" + std::to_string(i) + ".csv";
        test_files.push_back(generateLargeCsv(records_per_file, filename));
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Process files concurrently
    std::vector<std::future<size_t>> futures;
    for (const auto& filepath : test_files) {
        futures.push_back(std::async(std::launch::async, [filepath]() {
            CsvExtractor extractor;
            std::unordered_map<std::string, std::any> config;
            config["filepath"] = filepath;
            config["batch_size"] = size_t(5000);
            
            ProcessingContext context;
            extractor.initialize(config, context);
            
            size_t count = 0;
            while (extractor.has_more_data()) {
                auto batch = extractor.extract_batch(5000, context);
                count += batch.size();
            }
            
            extractor.finalize(context);
            return count;
        }));
    }
    
    // Wait for all tasks to complete
    size_t total_processed = 0;
    for (auto& future : futures) {
        total_processed += future.get();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(total_processed, num_files * records_per_file);
    
    // Concurrent processing should be faster than sequential
    double records_per_second = static_cast<double>(total_processed) / (duration.count() / 1000.0);
    EXPECT_GE(records_per_second, 20000.0)
        << "Concurrent processing performance: " << records_per_second << " records/sec";
}

// Test memory usage with large datasets
TEST_F(PerformanceTest, MemoryUsageValidation) {
    const size_t num_records = 500000;
    std::string filepath = generateLargeCsv(num_records, "memory_test.csv");
    
    // Get initial memory usage
    auto get_memory_usage = []() -> size_t {
        std::ifstream statm("/proc/self/statm");
        if (statm.is_open()) {
            size_t size, resident, shared, text, lib, data, dt;
            statm >> size >> resident >> shared >> text >> lib >> data >> dt;
            return resident * getpagesize(); // RSS in bytes
        }
        return 0;
    };
    
    size_t initial_memory = get_memory_usage();
    
    {
        CsvExtractor extractor;
        std::unordered_map<std::string, std::any> config;
        config["filepath"] = filepath;
        config["batch_size"] = size_t(1000); // Small batches to test streaming
        
        ProcessingContext context;
        extractor.initialize(config, context);
        
        size_t processed = 0;
        size_t max_memory = initial_memory;
        
        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(1000, context);
            processed += batch.size();
            
            // Check memory usage periodically
            if (processed % 50000 == 0) {
                size_t current_memory = get_memory_usage();
                max_memory = std::max(max_memory, current_memory);
            }
        }
        
        extractor.finalize(context);
        
        EXPECT_EQ(processed, num_records);
        
        // Memory usage should not grow excessively (less than 100MB for this test)
        size_t memory_increase = max_memory - initial_memory;
        EXPECT_LT(memory_increase, 100 * 1024 * 1024) // 100MB limit
            << "Memory usage increased by: " << memory_increase / (1024 * 1024) << " MB";
    }
    
    // Memory should be cleaned up after extraction
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    size_t final_memory = get_memory_usage();
    size_t final_increase = final_memory - initial_memory;
    
    // Allow some memory overhead but should be much less than peak usage
    EXPECT_LT(final_increase, 20 * 1024 * 1024) // 20MB limit
        << "Memory not properly cleaned up. Final increase: " << final_increase / (1024 * 1024) << " MB";
}

// =============================================================================
// NEW TEST SUITE 2: Integration Testing
// =============================================================================

/**
 * @file extract_integration_test.cpp
 * @brief Integration tests for extract module components
 */

class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_integration_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test complete ETL pipeline integration
TEST_F(IntegrationTest, CompleteETLPipelineIntegration) {
    // Create test CSV file
    std::string csv_content = 
        "patient_id,birth_date,gender,ethnicity\n"
        "1,1980-05-15,M,Hispanic\n"
        "2,1975-12-22,F,Caucasian\n"
        "3,1990-03-08,M,African American\n";
    
    std::filesystem::path csv_file = test_dir_ / "patients.csv";
    std::ofstream file(csv_file);
    file << csv_content;
    file.close();
    
    // Test end-to-end extraction using BatchExtractor
    auto extractor = create_extractor_auto(csv_file.string());
    ASSERT_NE(extractor, nullptr);
    
    BatchExtractor::Config config;
    config.batch_size = 2;
    config.max_records = 0; // No limit
    
    std::vector<std::string> processed_patients;
    config.progress_callback = [&processed_patients](size_t current, size_t total) {
        // Progress tracking integration
        EXPECT_LE(current, total);
    };
    
    BatchExtractor batch_extractor(std::move(extractor), config);
    
    // Extract with callback processing
    size_t total_processed = batch_extractor.extract_with_callback(
        [&processed_patients](const RecordBatch& batch) {
            for (const auto& record : batch.getRecords()) {
                if (record.hasField("patient_id")) {
                    processed_patients.push_back(
                        std::any_cast<std::string>(record.getField("patient_id")));
                }
            }
        });
    
    EXPECT_EQ(total_processed, 3);
    EXPECT_EQ(processed_patients.size(), 3);
    
    // Verify all patients were processed
    std::sort(processed_patients.begin(), processed_patients.end());
    EXPECT_EQ(processed_patients[0], "1");
    EXPECT_EQ(processed_patients[1], "2");
    EXPECT_EQ(processed_patients[2], "3");
    
    // Test statistics integration
    auto stats = batch_extractor.get_statistics();
    EXPECT_TRUE(stats.contains("batch_size"));
    EXPECT_EQ(std::any_cast<size_t>(stats["batch_size"]), 2);
}

// Test factory integration with configuration
TEST_F(IntegrationTest, ExtractorFactoryConfigurationIntegration) {
    // Test CSV extractor creation and configuration
    ExtractorConfigBuilder csv_builder("csv");
    
    std::string csv_file = (test_dir_ / "test.csv").string();
    std::ofstream(csv_file) << "id,name\n1,test\n2,test2\n";
    
    auto csv_extractor = csv_builder
        .with_file(csv_file)
        .set("has_header", true)
        .set("delimiter", ',')
        .with_columns({"id", "name"})
        .build();
    
    ASSERT_NE(csv_extractor, nullptr);
    EXPECT_EQ(csv_extractor->get_type(), "csv");
    
    // Test JSON extractor creation
    ExtractorConfigBuilder json_builder("json");
    
    std::string json_file = (test_dir_ / "test.json").string();
    std::ofstream(json_file) << R"([{"id": 1, "name": "test"}, {"id": 2, "name": "test2"}])";
    
    auto json_extractor = json_builder
        .with_file(json_file)
        .set("flatten_nested", true)
        .build();
    
    ASSERT_NE(json_extractor, nullptr);
    EXPECT_EQ(json_extractor->get_type(), "json");
    
    // Test extractor type information integration
    auto type_info = get_extractor_info();
    EXPECT_FALSE(type_info.empty());
    
    bool found_csv = false;
    bool found_json = false;
    for (const auto& info : type_info) {
        if (info.type == "csv") {
            found_csv = true;
            EXPECT_FALSE(info.description.empty());
            EXPECT_FALSE(info.required_params.empty());
            EXPECT_FALSE(info.example_config.empty());
        } else if (info.type == "json") {
            found_json = true;
        }
    }
    
    EXPECT_TRUE(found_csv);
    EXPECT_TRUE(found_json);
}

// =============================================================================
// NEW TEST SUITE 3: Error Recovery and Resilience Testing
// =============================================================================

/**
 * @file extract_resilience_test.cpp
 * @brief Resilience and error recovery tests
 */

class ResilienceTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_resilience_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test recovery from file system errors
TEST_F(ResilienceTest, FileSystemErrorRecovery) {
    std::string csv_content = "id,value\n1,100\n2,200\n";
    std::filesystem::path csv_file = test_dir_ / "recovery_test.csv";
    std::ofstream(csv_file) << csv_content;
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file.string();
    config["continue_on_error"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Extract first batch successfully
    auto batch1 = extractor.extract_batch(1, context);
    EXPECT_EQ(batch1.size(), 1);
    
    // Simulate file system error by removing file
    std::filesystem::remove(csv_file);
    
    // Extractor should handle the error gracefully
    EXPECT_NO_THROW({
        auto batch2 = extractor.extract_batch(1, context);
        // May return empty batch due to file error
    });
    
    // Statistics should reflect the error
    auto stats = extractor.get_statistics();
    EXPECT_GE(std::any_cast<size_t>(stats.at("extracted_count")), 1);
}

// Test handling of corrupted data with recovery
TEST_F(ResilienceTest, CorruptedDataRecovery) {
    // Create file with mixed valid and corrupted data
    std::string mixed_content = 
        "id,name,value\n"                    // Valid header
        "1,\"John Doe\",100.50\n"           // Valid record
        "2,\"Jane Smith\",200\n"            // Valid record
        "3,\"Corrupted,missing quote,300\n" // Corrupted - missing closing quote
        "4,\"Bob Johnson\",400\n"           // Valid record
        "5,\"Alice\",invalid_number\n"      // Invalid number
        "6,\"Charlie Brown\",600.75\n";     // Valid record
    
    std::filesystem::path csv_file = test_dir_ / "corrupted.csv";
    std::ofstream(csv_file) << mixed_content;
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file.string();
    config["continue_on_error"] = true;
    config["batch_size"] = size_t(10);
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    std::vector<Record> all_records;
    size_t error_count = 0;
    
    while (extractor.has_more_data()) {
        try {
            auto batch = extractor.extract_batch(10, context);
            for (const auto& record : batch.getRecords()) {
                all_records.push_back(record);
            }
        } catch (const std::exception& e) {
            error_count++;
            // Should continue processing despite errors
        }
    }
    
    // Should extract valid records despite corruption
    EXPECT_GE(all_records.size(), 4); // At least 4 valid records
    EXPECT_LE(all_records.size(), 6); // At most 6 records total
    
    // Verify that valid records were extracted correctly
    bool found_john = false;
    bool found_charlie = false;
    
    for (const auto& record : all_records) {
        if (record.hasField("name")) {
            std::string name = std::any_cast<std::string>(record.getField("name"));
            if (name.find("John") != std::string::npos) {
                found_john = true;
            } else if (name.find("Charlie") != std::string::npos) {
                found_charlie = true;
            }
        }
    }
    
    EXPECT_TRUE(found_john);
    EXPECT_TRUE(found_charlie);
    
    // Check error statistics
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("error_count"));
}

// Test resource exhaustion scenarios
TEST_F(ResilienceTest, ResourceExhaustionHandling) {
    // Create a very large CSV file to test memory limits
    std::filesystem::path large_file = test_dir_ / "large.csv";
    std::ofstream file(large_file);
    
    file << "id,data\n";
    
    // Create file with large text fields to test memory pressure
    const std::string large_text(10000, 'A'); // 10KB text per record
    
    for (int i = 1; i <= 1000; ++i) {
        file << i << ",\"" << large_text << "\"\n";
        
        if (i % 100 == 0) {
            file.flush();
        }
    }
    file.close();
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = large_file.string();
    config["batch_size"] = size_t(10); // Small batches to manage memory
    config["continue_on_error"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    size_t total_extracted = 0;
    size_t batch_count = 0;
    
    // Extract with memory monitoring
    while (extractor.has_more_data() && batch_count < 100) { // Safety limit
        auto batch = extractor.extract_batch(10, context);
        total_extracted += batch.size();
        batch_count++;
        
        // Force garbage collection hint
        if (batch_count % 10 == 0) {
            // Small delay to allow memory management
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    extractor.finalize(context);
    
    // Should have extracted significant amount of data
    EXPECT_GT(total_extracted, 500);
    
    // Verify statistics
    auto stats = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), total_extracted);
}

// =============================================================================
// NEW TEST SUITE 4: Thread Safety and Concurrency Testing
// =============================================================================

/**
 * @file extract_concurrency_test.cpp
 * @brief Thread safety and concurrency tests
 */

class ConcurrencyTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_concurrency_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test factory thread safety
TEST_F(ConcurrencyTest, ExtractorFactoryThreadSafety) {
    const int num_threads = 10;
    const int ops_per_thread = 100;
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> error_count{0};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([t, ops_per_thread, &success_count, &error_count]() {
            for (int op = 0; op < ops_per_thread; ++op) {
                try {
                    // Register and create extractors concurrently
                    std::string type_name = std::format("thread_{}_{}", t, op);
                    
                    ExtractorFactoryRegistry::register_type(type_name, []() {
                        return std::make_unique<CsvExtractor>();
                    });
                    
                    // Verify registration
                    if (ExtractorFactoryRegistry::is_type_registered(type_name)) {
                        auto extractor = ExtractorFactoryRegistry::create(type_name);
                        if (extractor) {
                            success_count++;
                        }
                    }
                } catch (const std::exception& e) {
                    error_count++;
                }
            }
        });
    }
    
    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All operations should succeed
    EXPECT_EQ(success_count.load(), num_threads * ops_per_thread);
    EXPECT_EQ(error_count.load(), 0);
    
    // Verify all types were registered
    auto registered_types = ExtractorFactoryRegistry::get_registered_types();
    EXPECT_GE(registered_types.size(), num_threads * ops_per_thread);
}

// Test concurrent file processing
TEST_F(ConcurrencyTest, ConcurrentFileProcessing) {
    const int num_files = 8;
    const int records_per_file = 1000;
    
    // Create test files
    std::vector<std::string> file_paths;
    for (int i = 0; i < num_files; ++i) {
        std::filesystem::path file_path = test_dir_ / std::format("concurrent_{}.csv", i);
        std::ofstream file(file_path);
        
        file << "id,thread_id,value\n";
        for (int j = 1; j <= records_per_file; ++j) {
            file << j << "," << i << "," << (j * i) << "\n";
        }
        file.close();
        
        file_paths.push_back(file_path.string());
    }
    
    // Process files concurrently
    std::vector<std::future<std::pair<size_t, std::string>>> futures;
    std::atomic<int> active_extractors{0};
    
    for (int i = 0; i < num_files; ++i) {
        futures.push_back(std::async(std::launch::async, 
            [&file_paths, i, &active_extractors]() -> std::pair<size_t, std::string> {
                
                active_extractors++;
                
                try {
                    CsvExtractor extractor;
                    std::unordered_map<std::string, std::any> config;
                    config["filepath"] = file_paths[i];
                    config["batch_size"] = size_t(100);
                    
                    ProcessingContext context;
                    extractor.initialize(config, context);
                    
                    size_t count = 0;
                    while (extractor.has_more_data()) {
                        auto batch = extractor.extract_batch(100, context);
                        count += batch.size();
                    }
                    
                    extractor.finalize(context);
                    active_extractors--;
                    
                    return {count, "success"};
                } catch (const std::exception& e) {
                    active_extractors--;
                    return {0, e.what()};
                }
            }));
    }
    
    // Collect results
    size_t total_records = 0;
    int successful_extractions = 0;
    
    for (auto& future : futures) {
        auto [count, status] = future.get();
        if (status == "success") {
            total_records += count;
            successful_extractions++;
            EXPECT_EQ(count, records_per_file);
        } else {
            FAIL() << "Extraction failed: " << status;
        }
    }
    
    EXPECT_EQ(successful_extractions, num_files);
    EXPECT_EQ(total_records, num_files * records_per_file);
    EXPECT_EQ(active_extractors.load(), 0); // All should be finished
}

// Test connection pool under concurrent load
TEST_F(ConcurrencyTest, ConnectionPoolConcurrentLoad) {
    std::atomic<int> connection_count{0};
    std::atomic<int> max_concurrent{0};
    std::atomic<int> current_concurrent{0};
    
    auto connection_factory = [&connection_count, &max_concurrent, &current_concurrent]() -> std::unique_ptr<IDatabaseConnection> {
        connection_count++;
        current_concurrent++;
        
        int current = current_concurrent.load();
        int expected_max = max_concurrent.load();
        while (current > expected_max && 
               !max_concurrent.compare_exchange_weak(expected_max, current)) {
            expected_max = max_concurrent.load();
        }
        
        // Simulate connection work
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        auto conn = std::make_unique<MockDatabaseConnection>();
        ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
        
        current_concurrent--;
        return std::move(conn);
    };
    
    ConnectionPool pool(2, 8, connection_factory);
    
    const int num_threads = 16;
    const int ops_per_thread = 10;
    
    std::vector<std::thread> threads;
    std::atomic<int> successful_ops{0};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&pool, ops_per_thread, &successful_ops]() {
            for (int op = 0; op < ops_per_thread; ++op) {
                try {
                    auto conn = pool.acquire(1000); // 1 second timeout
                    if (conn) {
                        // Simulate work with connection
                        std::this_thread::sleep_for(std::chrono::milliseconds(5));
                        
                        pool.release(std::move(conn));
                        successful_ops++;
                    }
                } catch (const std::exception& e) {
                    // Some operations may timeout under heavy load
                }
            }
        });
    }
    
    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Most operations should succeed
    EXPECT_GE(successful_ops.load(), num_threads * ops_per_thread * 0.8); // 80% success rate
    
    // Verify pool statistics
    auto stats = pool.get_statistics();
    EXPECT_GT(stats.total_acquisitions, 0);
    EXPECT_EQ(stats.active_connections, 0); // All should be returned
    
    // Connection count should be reasonable (not creating excessive connections)
    EXPECT_LE(connection_count.load(), 20); // Reasonable upper bound
    EXPECT_LE(max_concurrent.load(), 8);    // Should not exceed pool limit
}


