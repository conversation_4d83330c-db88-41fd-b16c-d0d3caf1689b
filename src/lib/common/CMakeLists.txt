# Common library CMakeLists.txt

add_library(omop_common STATIC)

set(COMMON_SOURCES
    configuration.cpp
    exceptions.cpp
    http_client.cpp
    logging.cpp
    metrics_collector.cpp
    utilities.cpp
    validation.cpp
)

set(COMMON_HEADERS
    configuration.h
    exceptions.h
    http_client.h
    logging.h
    metrics_collector.h
    utilities.h
    validation.h
)

target_sources(omop_common PRIVATE ${COMMON_SOURCES})

omop_configure_library(omop_common
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        yaml-cpp::yaml-cpp
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        fmt::fmt
        spdlog::spdlog
        OpenSSL::SSL
        OpenSSL::Crypto
        Threads::Threads
        ${UUID_LIBRARIES}
        CURL::libcurl
    HEADERS
        ${COMMON_HEADERS}
)