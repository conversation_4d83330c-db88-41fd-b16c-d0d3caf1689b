--- a/src/lib/extract/csv_extractor.h
+++ b/src/lib/extract/csv_extractor.h
@@ -311,6 +311,13 @@ protected:
      */
     bool read_complete_record(std::string& line);
 
+private:
+    // Private helper methods for CompressedCsvExtractor
+    void decompress_gzip(const std::string& src, const std::string& dst);
+    void decompress_zip(const std::string& src, const std::string& dst);
+    void decompress_bzip2(const std::string& src, const std::string& dst);
+    void decompress_xz(const std::string& src, const std::string& dst);
+
 protected:
     // CSV-specific members (protected so derived classes can access)
     std::ifstream file_stream_;                    ///< CSV file stream